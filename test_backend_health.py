#!/usr/bin/env python3
import requests
import json
import time
import subprocess
import sys
import os

def run_db_init():
    """运行数据库初始化脚本"""
    print("🔧 运行数据库初始化...")
    try:
        result = subprocess.run([
            sys.executable, "backend/init_db.py"
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        print("数据库初始化输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 运行数据库初始化失败: {e}")
        return False

def test_backend_health():
    """测试后端健康状态"""
    backend_url = "http://localhost:8000"
    
    print("🔍 测试后端连接...")
    
    # 0. 首先检查后端是否运行
    try:
        response = requests.get(f"{backend_url}/", timeout=3)
        print(f"✅ 后端服务运行中 (状态码: {response.status_code})")
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接后端: {e}")
        print("请确保后端服务正在运行: cd backend && python main.py")
        return False
    
    # 1. 健康检查
    try:
        response = requests.get(f"{backend_url}/health", timeout=5)
        print(f"\n🏥 健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            health_data = response.json()
            print("✅ 健康检查通过:")
            print(json.dumps(health_data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 健康检查失败: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 健康检查请求失败: {e}")
        return False
    
    # 2. 测试 NPCs 端点
    try:
        print("\n👥 测试 NPCs 端点...")
        response = requests.get(f"{backend_url}/npcs", timeout=10)
        print(f"NPCs 端点状态码: {response.status_code}")
        if response.status_code == 200:
            npcs_data = response.json()
            print("✅ NPCs 端点结果:")
            print(json.dumps(npcs_data, indent=2, ensure_ascii=False))
            
            if not npcs_data.get("npcs"):
                print("⚠️ 没有找到 NPC 数据，尝试初始化数据库...")
                if run_db_init():
                    print("✅ 数据库初始化完成，重新测试...")
                    # 重新测试 NPCs
                    response = requests.get(f"{backend_url}/npcs", timeout=10)
                    if response.status_code == 200:
                        npcs_data = response.json()
                        print("✅ 重新获取 NPCs 成功:")
                        print(json.dumps(npcs_data, indent=2, ensure_ascii=False))
        else:
            print(f"⚠️ NPCs 端点响应: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"⚠️ NPCs 端点请求失败: {e}")
    
    # 3. 测试"1+1等于几"端点
    try:
        print("\n🧮 测试 '1+1等于几' 问答...")
        response = requests.get(f"{backend_url}/test", timeout=30)
        print(f"测试端点状态码: {response.status_code}")
        if response.status_code == 200:
            test_data = response.json()
            if test_data.get("status") == "success":
                print("✅ 测试端点成功:")
                print(json.dumps(test_data, indent=2, ensure_ascii=False))
                return True
            else:
                print("⚠️ 测试端点返回错误:")
                print(json.dumps(test_data, indent=2, ensure_ascii=False))
                
                # 如果是 NPC 不存在的错误，尝试初始化数据库
                if "NPC" in str(test_data.get("error", "")):
                    print("🔧 检测到 NPC 相关错误，尝试初始化数据库...")
                    if run_db_init():
                        print("✅ 数据库初始化完成，重新测试...")
                        time.sleep(2)  # 等待一下
                        response = requests.get(f"{backend_url}/test", timeout=30)
                        if response.status_code == 200:
                            test_data = response.json()
                            print("✅ 重新测试结果:")
                            print(json.dumps(test_data, indent=2, ensure_ascii=False))
                            return test_data.get("status") == "success"
        else:
            print(f"⚠️ 测试端点响应: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"⚠️ 测试端点请求失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🚀 开始后端健康检查...")
    print("=" * 50)
    
    success = test_backend_health()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！后端服务正常运行。")
        return 0
    else:
        print("❌ 部分测试失败，请检查上述错误信息。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)