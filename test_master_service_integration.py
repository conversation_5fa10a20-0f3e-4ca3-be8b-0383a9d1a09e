import sys
import os
import json
from dotenv import load_dotenv

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 加载环境变量
load_dotenv(os.path.join(os.path.dirname(__file__), 'backend', '.env'))

from utils.reranker_service import RerankerService

def test_master_service_integration():
    """
    测试MasterService与RerankerService的集成
    """
    print("开始测试MasterService与RerankerService的集成...")
    
    try:
        # 初始化Reranker服务
        reranker_service = RerankerService()
        print("✓ Reranker服务初始化成功")
        
        # 模拟MasterService中的工具列表
        available_tools = [
            {
                "name": "fetch_news",
                "description": "获取新闻。可以根据多种条件进行筛选和排序，比如获取用户已读的、未读的、最近读过的，或者根据特定主题进行搜索。"
            },
            {
                "name": "search_and_summarize",
                "description": "搜索并总结网络信息。当用户需要了解实时信息或超出个人知识范围的内容时使用，如天气、股票价格等。"
            },
            {
                "name": "recall_current_activity",
                "description": "回忆当前活动。用于获取角色当前的状态和正在进行的活动。"
            },
            {
                "name": "get_weather_info",
                "description": "获取天气信息。提供当前位置或指定位置的天气情况，包括温度、湿度、风力等。"
            },
            {
                "name": "tell_joke",
                "description": "讲笑话。当用户需要娱乐或放松时使用，可以提供各种类型的笑话。"
            }
        ]
        
        # 测试不同用户问题的工具推荐
        test_cases = [
            {
                "question": "今天北京的天气怎么样？",
                "expected_tool": "get_weather_info"
            },
            {
                "question": "给我找一些关于人工智能的最新新闻",
                "expected_tool": "fetch_news"
            },
            {
                "question": "你能告诉我量子计算是什么吗？",
                "expected_tool": "search_and_summarize"
            },
            {
                "question": "你现在在做什么？",
                "expected_tool": "recall_current_activity"
            },
            {
                "question": "讲个笑话让我开心一下",
                "expected_tool": "tell_joke"
            }
        ]
        
        print("\n测试工具推荐准确性:")
        correct_recommendations = 0
        
        for i, test_case in enumerate(test_cases):
            question = test_case["question"]
            expected_tool = test_case["expected_tool"]
            
            # 获取最相关的工具
            recommended_tool = reranker_service.get_top_relevant_tool(question, available_tools)
            
            if recommended_tool and recommended_tool["name"] == expected_tool:
                print(f"   {i+1}. 问题: '{question}'")
                print(f"      推荐工具: {recommended_tool['name']} ✓")
                correct_recommendations += 1
            else:
                print(f"   {i+1}. 问题: '{question}'")
                if recommended_tool:
                    print(f"      推荐工具: {recommended_tool['name']} (期望: {expected_tool}) ✗")
                else:
                    print(f"      推荐工具: 无 (期望: {expected_tool}) ✗")
        
        print(f"\n推荐准确率: {correct_recommendations}/{len(test_cases)} ({correct_recommendations/len(test_cases)*100:.1f}%)")
        
        # 演示如何在MasterService中使用reranker进行工具选择
        print("\n模拟MasterService中的工具选择流程:")
        user_question = "我想知道最近有什么科技新闻"
        
        print(f"用户问题: {user_question}")
        
        # 使用reranker服务获取最相关的工具
        best_tool = reranker_service.get_top_relevant_tool(user_question, available_tools)
        
        if best_tool:
            print(f"推荐工具: {best_tool['name']}")
            print(f"工具描述: {best_tool['description']}")
            print(f"相关性得分: {best_tool['relevance_score']:.4f}")
            
            # 根据推荐的工具执行相应的操作
            if best_tool["name"] == "fetch_news":
                print("执行操作: 调用新闻服务获取科技新闻...")
            elif best_tool["name"] == "search_and_summarize":
                print("执行操作: 调用搜索服务获取科技相关信息...")
            # ... 其他工具的处理逻辑
        else:
            print("未能推荐合适的工具")
        
        print("\n✓ 集成测试完成")
        
    except Exception as e:
        print(f"✗ 集成测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_master_service_integration()
