import sys
import os
import json
import glob

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def load_mcp_config(config_path):
    """
    加载MCP配置文件
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"  无法加载配置文件 {config_path}: {e}")
        return None

def register_mcp_servers_from_config(config_data, config_name):
    """
    从配置数据注册MCP服务器
    """
    from functions.master_service import master_service
    
    registered_servers = []
    mcp_servers = config_data.get("mcpServers", {})
    
    for server_name, server_config in mcp_servers.items():
        command = server_config.get("command")
        args = server_config.get("args", [])
        env = server_config.get("env", {})
        
        # 创建服务器URL表示
        server_url = f"local://{command} {' '.join(args)}"
        
        # 注册服务器
        result = master_service.call(
            "register_server", 
            server_name=f"{config_name}-{server_name}", 
            server_url=server_url,
            server_info={
                "command": command,
                "args": args,
                "env": env,
                "type": "local_command",
                "source_config": config_name
            }
        )
        
        if result:
            registered_servers.append(f"{config_name}-{server_name}")
            print(f"  ✓ 注册服务器: {config_name}-{server_name}")
        else:
            print(f"  ✗ 注册服务器失败: {config_name}-{server_name}")
    
    return registered_servers

def test_multiple_mcp_configs():
    """
    测试多个MCP配置文件
    """
    print("开始测试多个MCP配置文件...")
    
    try:
        # Import the services directly
        from services.mcp_service import mcp_service
        from functions.master_service import master_service

        # 1. 查找所有MCP配置文件
        print("\n1. 查找MCP配置文件...")
        mcp_config_dir = os.path.join(os.path.dirname(__file__), 'backend', 'mcp_config')
        config_files = glob.glob(os.path.join(mcp_config_dir, "*.json"))
        
        print(f"  找到 {len(config_files)} 个配置文件:")
        for config_file in config_files:
            print(f"  - {os.path.basename(config_file)}")
        
        # 2. 加载并注册所有配置文件中的服务器
        print("\n2. 加载并注册MCP服务器...")
        all_registered_servers = []
        
        for config_file in config_files:
            config_name = os.path.splitext(os.path.basename(config_file))[0]
            print(f"\n  处理配置文件: {config_name}")
            
            # 加载配置
            config_data = load_mcp_config(config_file)
            if config_data:
                # 注册服务器
                registered_servers = register_mcp_servers_from_config(config_data, config_name)
                all_registered_servers.extend(registered_servers)
            else:
                print(f"  无法加载配置文件: {config_name}")
        
        # 3. 显示所有已注册的服务器
        print("\n3. 所有已注册的服务器:")
        servers = master_service.call("list_servers")
        for server in servers:
            print(f"  - {server['name']}: {server['url']}")
            if 'source_config' in server:
                print(f"    来源配置: {server['source_config']}")
        
        print(f"\n  总共注册了 {len(servers)} 个服务器")
        
        # 4. 获取所有工具用于reranker
        print("\n4. 获取所有工具用于reranker...")
        all_tools = master_service.get_all_tools_for_reranker()
        print(f"✓ 成功获取到 {len(all_tools)} 个工具用于reranker")
        
        if all_tools:
            print("  可用工具:")
            for i, tool in enumerate(all_tools[:10], 1):  # 只显示前10个工具
                print(f"    {i}. {tool['name']}: {tool['description'][:60]}...")
            if len(all_tools) > 10:
                print(f"    ... 还有 {len(all_tools) - 10} 个工具")
        else:
            print("  暂无工具信息（使用模拟数据）")
            # 模拟一些工具
            mock_tools = [
                {"name": "search_web", "description": "搜索网络信息"},
                {"name": "get_weather", "description": "获取天气信息"},
                {"name": "find_recipe", "description": "查找菜谱"},
                {"name": "query_map", "description": "查询地图信息"},
                {"name": "search_video", "description": "搜索视频内容"}
            ]
            for tool in mock_tools:
                print(f"    - {tool['name']}: {tool['description']}")
        
        # 5. 模拟执行不同类型工具
        print("\n5. 模拟执行工具...")
        test_scenarios = [
            {
                "tool_name": "search_web",
                "query": "人工智能最新发展",
                "description": "网络搜索工具"
            },
            {
                "tool_name": "get_weather",
                "query": "北京天气",
                "description": "天气查询工具"
            },
            {
                "tool_name": "find_recipe",
                "query": "红烧肉做法",
                "description": "菜谱查询工具"
            },
            {
                "tool_name": "query_map",
                "query": "附近餐厅",
                "description": "地图查询工具"
            },
            {
                "tool_name": "search_video",
                "query": "Python教程",
                "description": "视频搜索工具"
            }
        ]
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n  执行测试场景 {i}: {scenario['description']}")
            print(f"    工具: {scenario['tool_name']}")
            print(f"    查询: {scenario['query']}")
            
            result = master_service.execute_mcp_tool(
                scenario['tool_name'],
                query=scenario['query']
            )
            
            if result["status"] == "success":
                print(f"    ✓ 执行成功")
                if "result" in result:
                    result_summary = str(result['result'])[:80] + "..." if len(str(result['result'])) > 80 else str(result['result'])
                    print(f"    结果: {result_summary}")
            else:
                print(f"    ✗ 执行失败: {result.get('error', '未知错误')}")
        
        # 6. 按配置来源分组显示工具
        print("\n6. 按配置来源分组工具...")
        servers = master_service.call("list_servers")
        config_groups = {}
        
        for server in servers:
            source_config = server.get('source_config', 'unknown')
            if source_config not in config_groups:
                config_groups[source_config] = []
            config_groups[source_config].append(server['name'])
        
        for config_name, server_names in config_groups.items():
            print(f"  配置 '{config_name}' 的服务器:")
            for server_name in server_names:
                tools = master_service.call("get_server_tools", server_name=server_name)
                print(f"    - {server_name}: {len(tools)} 个工具")
                if tools:
                    for tool in tools[:3]:  # 只显示前3个工具
                        print(f"      * {tool['name']}: {tool['description'][:50]}...")
                    if len(tools) > 3:
                        print(f"      * ... 还有 {len(tools) - 3} 个工具")
        
        # 7. 测试配置文件操作
        print("\n7. 测试配置文件操作...")
        
        # 创建一个包含多个服务器的配置示例
        sample_config = {
            "servers": [
                {
                    "name": "sample_web_search",
                    "url": "http://web-search-service:8000",
                    "type": "web_service"
                },
                {
                    "name": "sample_weather_service",
                    "url": "http://weather-service:8000",
                    "type": "web_service"
                }
            ],
            "default_server": "sample_web_search"
        }
        
        # 初始化配置
        init_result = master_service.initialize_mcp_config(sample_config)
        print(f"✓ 配置初始化: {'成功' if init_result else '失败'}")
        
        # 验证配置
        servers_after_init = master_service.call("list_servers")
        print(f"  初始化后服务器数量: {len(servers_after_init)}")
        
        # 删除配置
        delete_result = master_service.delete_mcp_config()
        print(f"✓ 配置删除: {'成功' if delete_result else '失败'}")
        
        print("\n" + "="*50)
        print("✓ 多个MCP配置文件测试完成")
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def demo_multi_mcp_workflow():
    """
    演示多MCP工作流程
    """
    print("\n" + "="*50)
    print("演示多MCP工作流程")
    print("="*50)
    
    try:
        # Import the services directly
        from services.mcp_service import mcp_service
        from functions.master_service import master_service

        # 模拟实际使用场景：多MCP服务器协同工作
        print("场景：配置多个MCP服务器并协同处理用户请求")
        
        # 1. 加载并注册多个MCP配置
        print("\n1. 加载并注册MCP配置...")
        mcp_config_dir = os.path.join(os.path.dirname(__file__), 'backend', 'mcp_config')
        config_files = [
            "12306-mcp.json",
            "amap-mcp-server.json", 
            "bilibili-mcp.json",
            "mcp-trends-hub.json"
        ]
        
        registered_servers = []
        for config_file in config_files:
            config_path = os.path.join(mcp_config_dir, config_file)
            if os.path.exists(config_path):
                config_name = os.path.splitext(os.path.basename(config_file))[0]
                print(f"\n  加载配置: {config_name}")
                
                config_data = load_mcp_config(config_path)
                if config_data:
                    servers = register_mcp_servers_from_config(config_data, config_name)
                    registered_servers.extend(servers)
                else:
                    print(f"  无法加载配置: {config_name}")
            else:
                print(f"  配置文件不存在: {config_file}")
        
        # 2. 显示所有已注册的服务器
        print("\n2. 所有已注册的服务器:")
        servers = master_service.call("list_servers")
        for server in servers:
            print(f"  - {server['name']}: {server['url']}")
            if 'source_config' in server:
                print(f"    来源: {server['source_config']}")
                print(f"    命令: {server.get('command')} {' '.join(server.get('args', []))}")
        
        # 3. 模拟不同类型的用户查询
        print("\n3. 用户查询处理演示...")
        user_scenarios = [
            {
                "query": "我想查询明天北京到上海的高铁",
                "expected_servers": ["12306-mcp-12306-mcp"],
                "description": "火车票查询"
            },
            {
                "query": "帮我查一下附近的餐厅",
                "expected_servers": ["amap-mcp-server-amap-mcp-server"],
                "description": "地图服务查询"
            },
            {
                "query": "找一些Python学习的视频",
                "expected_servers": ["bilibili-mcp-bilibili-mcp"],
                "description": "视频内容搜索"
            },
            {
                "query": "最近有什么技术趋势",
                "expected_servers": ["mcp-trends-hub-trends-hub"],
                "description": "趋势分析"
            }
        ]
        
        # 模拟工具推荐和执行（实际应该使用reranker服务）
        for i, scenario in enumerate(user_scenarios, 1):
            print(f"\n  场景 {i}: {scenario['description']}")
            print(f"    用户查询: {scenario['query']}")
            
            # 模拟工具执行
            result = master_service.execute_mcp_tool(
                "search_query_processor",  # 通用处理工具
                query=scenario['query']
            )
            
            if result["status"] == "success":
                print(f"    处理结果: ✓ 成功")
                if "result" in result:
                    print(f"    {result['result']}")
            else:
                print(f"    处理结果: ✗ 失败 - {result.get('error', '未知错误')}")
        
        # 4. 展示工具发现能力
        print("\n4. 工具发现与整合...")
        all_tools = master_service.get_all_tools_for_reranker()
        print(f"  发现 {len(all_tools)} 个可用工具")
        
        # 按服务器分组显示工具
        for server in servers:
            server_tools = master_service.call("get_server_tools", server_name=server['name'])
            if server_tools:
                print(f"  {server['name']} 提供 {len(server_tools)} 个工具:")
                for tool in server_tools[:3]:  # 显示前3个工具
                    print(f"    - {tool['name']}: {tool['description'][:50]}...")
                if len(server_tools) > 3:
                    print(f"    - ... 还有 {len(server_tools) - 3} 个工具")
        
        # 5. 模拟智能路由
        print("\n5. 智能路由演示...")
        routing_scenarios = [
            {
                "query": "从北京到上海怎么走？",
                "expected_server": "amap-mcp-server-amap-mcp-server"
            },
            {
                "query": "我想看一些科技新闻",
                "expected_server": "mcp-trends-hub-trends-hub"
            },
            {
                "query": "帮我订一张去广州的票",
                "expected_server": "12306-mcp-12306-mcp"
            }
        ]
        
        for scenario in routing_scenarios:
            print(f"\n  路由场景:")
            print(f"    查询: {scenario['query']}")
            print(f"    推荐服务器: {scenario['expected_server']}")
            print(f"    路由状态: ✓ 准备就绪")
        
        # 6. 清理（在实际应用中可能不需要）
        print("\n6. 清理服务器...")
        servers = master_service.call("list_servers")
        for server in servers:
            result = master_service.call("unregister_server", server_name=server['name'])
            print(f"  注销 {server['name']}: {'✓' if result else '✗'}")
        
        print("\n" + "="*50)
        print("✓ 多MCP工作流程演示完成")

    except Exception as e:
        print(f"✗ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行多配置文件测试
    test_multiple_mcp_configs()
    
    # 运行多MCP工作流程演示
    demo_multi_mcp_workflow()
