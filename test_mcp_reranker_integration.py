import sys
import os
import json
from unittest.mock import Mock, patch

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def mock_reranker_service():
    """创建一个模拟的reranker服务"""
    mock_service = Mock()
    
    # 模拟工具数据
    mock_tools = [
        {
            "name": "search_and_summarize",
            "description": "搜索并总结网络信息。当用户需要了解实时信息或超出个人知识范围的内容时使用，如天气、股票价格等。"
        },
        {
            "name": "fetch_news",
            "description": "获取新闻。可以根据多种条件进行筛选和排序，比如获取用户已读的、未读的、最近读过的，或者根据特定主题进行搜索。"
        },
        {
            "name": "recall_current_activity",
            "description": "回忆当前活动。用于获取角色当前的状态和正在进行的活动。"
        },
        {
            "name": "recall_relevant_experiences",
            "description": "回忆相关经历。根据用户问题搜索角色过去的相似经历。"
        },
        {
            "name": "get_weather_info",
            "description": "获取天气信息。提供当前位置或指定位置的天气情况，包括温度、湿度、风力等。"
        }
    ]
    
    # 模拟相关性排序结果
    mock_ranked_tools = [
        {
            "name": "fetch_news",
            "description": "获取新闻。可以根据多种条件进行筛选和排序，比如获取用户已读的、未读的、最近读过的，或者根据特定主题进行搜索。",
            "relevance_score": 0.95
        },
        {
            "name": "search_and_summarize",
            "description": "搜索并总结网络信息。当用户需要了解实时信息或超出个人知识范围的内容时使用，如天气、股票价格等。",
            "relevance_score": 0.85
        },
        {
            "name": "recall_current_activity",
            "description": "回忆当前活动。用于获取角色当前的状态和正在进行的活动。",
            "relevance_score": 0.75
        }
    ]
    
    # 设置模拟方法的返回值
    mock_service.rank_tools_by_relevance.return_value = mock_ranked_tools
    mock_service.get_top_relevant_tool.return_value = mock_ranked_tools[0] if mock_ranked_tools else None
    
    return mock_service

def test_mcp_reranker_integration():
    """
    测试MCP服务与Reranker服务的集成
    """
    print("开始测试MCP服务与Reranker服务的集成...")
    
    # 使用patch来模拟reranker_service
    with patch('backend.utils.reranker_service.reranker_service', mock_reranker_service()):
        try:
            # Import the services directly
            from services.mcp_service import mcp_service
            from functions.master_service import master_service
            from utils.reranker_service import reranker_service

            # 1. 测试获取所有工具用于reranker过滤
            print("\n1. 测试获取所有工具用于reranker过滤...")
            tools = master_service.get_all_tools_for_reranker()
            print(f"✓ 成功获取到 {len(tools)} 个工具")
            
            # 验证工具格式
            for tool in tools:
                assert "name" in tool, "工具缺少name字段"
                assert "description" in tool, "工具缺少description字段"
                print(f"  - {tool['name']}: {tool['description'][:50]}...")
            
            # 2. 测试使用reranker对工具进行排序
            print("\n2. 测试使用reranker对工具进行排序...")
            user_question = "我想了解最新的科技新闻"
            
            # 使用reranker服务获取最相关的工具
            ranked_tools = reranker_service.rank_tools_by_relevance(user_question, tools)
            
            if ranked_tools:
                print(f"✓ 成功对 {len(ranked_tools)} 个工具进行相关性排序")
                print("相关性排序结果:")
                for i, tool in enumerate(ranked_tools[:3]):  # 只显示前3个
                    print(f"  {i+1}. {tool['name']} (相关性得分: {tool['relevance_score']:.4f})")
            else:
                print("✗ 未能对工具进行相关性排序")
            
            # 3. 测试获取最相关的单个工具
            print("\n3. 测试获取最相关的单个工具...")
            best_tool = reranker_service.get_top_relevant_tool(user_question, tools)
            
            if best_tool:
                print(f"✓ 推荐的最相关工具: {best_tool['name']}")
                print(f"  描述: {best_tool['description']}")
                print(f"  相关性得分: {best_tool['relevance_score']:.4f}")
            else:
                print("✗ 未能推荐最相关的工具")
            
            # 4. 测试MCP配置文件操作
            print("\n4. 测试MCP配置文件操作...")
            
            # 初始化配置
            sample_config = {
                "servers": [
                    {
                        "name": "test_server",
                        "url": "http://localhost:3000",
                        "tools": ["search_and_summarize", "fetch_news"]
                    }
                ],
                "default_server": "test_server"
            }
            
            init_result = master_service.initialize_mcp_config(sample_config)
            print(f"✓ 配置初始化: {'成功' if init_result else '失败'}")
            
            # 删除配置
            delete_result = master_service.delete_mcp_config()
            print(f"✓ 配置删除: {'成功' if delete_result else '失败'}")
            
            # 5. 测试执行MCP工具
            print("\n5. 测试执行MCP工具...")
            execution_result = master_service.execute_mcp_tool(
                "search_and_summarize", 
                query="人工智能发展趋势"
            )
            
            print(f"✓ 工具执行状态: {execution_result['status']}")
            if execution_result['status'] == 'success':
                print(f"  工具名称: {execution_result['tool_name']}")
                print(f"  执行时间: {execution_result['timestamp']}")
                if 'result' in execution_result:
                    print(f"  结果摘要: {str(execution_result['result'])[:100]}...")
            else:
                print(f"  错误信息: {execution_result.get('error', '未知错误')}")
            
            print("\n" + "="*50)
            print("✓ MCP与Reranker集成测试完成")
            
        except Exception as e:
            print(f"✗ 集成测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

def demo_mcp_tool_selection_workflow():
    """
    演示MCP工具选择工作流程
    """
    print("\n" + "="*50)
    print("演示MCP工具选择工作流程")
    print("="*50)
    
    # 使用patch来模拟reranker_service
    with patch('backend.utils.reranker_service.reranker_service', mock_reranker_service()):
        try:
            # Import the services directly
            from services.mcp_service import mcp_service
            from functions.master_service import master_service
            from utils.reranker_service import reranker_service

            # 模拟用户问题列表
            user_questions = [
                "今天北京的天气怎么样？",
                "给我找一些关于人工智能的最新新闻",
                "你能告诉我量子计算是什么吗？",
                "你现在在做什么？",
                "讲个笑话让我开心一下"
            ]
            
            # 获取所有可用工具
            available_tools = master_service.get_all_tools_for_reranker()
            print(f"系统中有 {len(available_tools)} 个可用工具")
            
            print("\n工具推荐结果:")
            print("-" * 50)
            
            for question in user_questions:
                # 使用reranker获取最相关的工具
                recommended_tool = reranker_service.get_top_relevant_tool(question, available_tools)
                
                print(f"用户问题: {question}")
                if recommended_tool:
                    print(f"  推荐工具: {recommended_tool['name']}")
                    print(f"  工具描述: {recommended_tool['description']}")
                    print(f"  相关性得分: {recommended_tool['relevance_score']:.4f}")
                    
                    # 模拟执行工具
                    print(f"  执行工具: ", end="")
                    execution_result = master_service.execute_mcp_tool(
                        recommended_tool['name'], 
                        query=question
                    )
                    if execution_result['status'] == 'success':
                        print("✓ 成功")
                    else:
                        print("✗ 失败")
                else:
                    print("  推荐工具: 无")
                print()

        except Exception as e:
            print(f"✗ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    # 运行集成测试
    test_mcp_reranker_integration()
    
    # 运行演示
    demo_mcp_tool_selection_workflow()
