import sys
import os
import json

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_mcp_functionality():
    """
    测试MCP功能：获取工具信息、配置管理、工具执行
    """
    print("开始测试MCP核心功能...")
    
    try:
        # Import the services directly
        from services.mcp_service import mcp_service
        from functions.master_service import master_service

        # 1. 测试获取所有工具的名称、描述，以便使用reranker 模型进行过滤
        print("\n1. 测试获取所有工具用于reranker过滤...")
        tools = master_service.get_all_tools_for_reranker()
        print(f"✓ 成功获取到 {len(tools)} 个工具")
        for i, tool in enumerate(tools, 1):
            print(f"  {i}. {tool['name']}: {tool['description'][:50]}...")
        
        # 2. 测试用户的config.json的初始化和删除
        print("\n2. 测试MCP配置文件操作...")
        
        # 初始化配置
        sample_config = {
            "servers": [
                {
                    "name": "test_server_1",
                    "url": "http://test1.example.com:3000",
                    "description": "测试服务器1"
                },
                {
                    "name": "test_server_2",
                    "url": "http://test2.example.com:3000",
                    "description": "测试服务器2"
                }
            ],
            "default_server": "test_server_1"
        }
        
        init_result = master_service.initialize_mcp_config(sample_config)
        print(f"✓ 配置初始化: {'成功' if init_result else '失败'}")
        
        # 验证配置是否正确保存
        servers = master_service.call("list_servers")
        print(f"  当前服务器数量: {len(servers)}")
        for server in servers:
            print(f"  - {server['name']}: {server['url']}")
        
        # 删除配置
        delete_result = master_service.delete_mcp_config()
        print(f"✓ 配置删除: {'成功' if delete_result else '失败'}")
        
        # 验证配置是否已删除
        servers_after_delete = master_service.call("list_servers")
        print(f"  删除后服务器数量: {len(servers_after_delete)}")
        
        # 3. 重新注册一些服务器用于后续测试
        print("\n3. 重新注册测试服务器...")
        master_service.call("register_server", server_name="search_server", server_url="http://search.example.com:3000")
        master_service.call("register_server", server_name="news_server", server_url="http://news.example.com:3000")
        
        # 4. 测试根据function call 结果做执行获取返回结果
        print("\n4. 测试执行MCP工具...")
        
        # 执行不同的工具
        test_cases = [
            {
                "tool_name": "search_and_summarize",
                "server_name": "search_server",
                "parameters": {"query": "人工智能发展趋势", "max_results": 5}
            },
            {
                "tool_name": "fetch_news",
                "server_name": "news_server",
                "parameters": {"topic": "科技", "limit": 10}
            },
            {
                "tool_name": "get_weather_info",
                "server_name": "search_server",
                "parameters": {"location": "北京", "unit": "celsius"}
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n  执行测试用例 {i}: {test_case['tool_name']}")
            result = master_service.execute_mcp_tool(
                test_case["tool_name"],
                server_name=test_case["server_name"],
                **test_case["parameters"]
            )
            
            if result["status"] == "success":
                print(f"  ✓ 工具执行状态: {result['status']}")
                print(f"    工具名称: {result['tool_name']}")
                print(f"    服务器: {result['server_name']}")
                print(f"    执行时间: {result['timestamp']}")
                if "result" in result:
                    # 简化显示结果
                    result_summary = str(result['result'])[:100] + "..." if len(str(result['result'])) > 100 else str(result['result'])
                    print(f"    结果摘要: {result_summary}")
            else:
                print(f"  ✗ 工具执行失败: {result.get('error', '未知错误')}")
        
        # 5. 测试获取特定服务器的工具信息
        print("\n5. 测试获取特定服务器的工具信息...")
        search_server_tools = master_service.call("get_server_tools", server_name="search_server")
        print(f"✓ 搜索服务器工具获取: {'成功' if search_server_tools is not None else '失败'}")
        if search_server_tools:
            print(f"  获取到 {len(search_server_tools)} 个工具")
            for tool in search_server_tools:
                print(f"    - {tool['name']}: {tool['description']}")
        
        news_server_tools = master_service.call("get_server_tools", server_name="news_server")
        print(f"✓ 新闻服务器工具获取: {'成功' if news_server_tools is not None else '失败'}")
        if news_server_tools:
            print(f"  获取到 {len(news_server_tools)} 个工具")
            for tool in news_server_tools:
                print(f"    - {tool['name']}: {tool['description']}")
        
        print("\n" + "="*50)
        print("✓ MCP核心功能测试完成")
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def demo_mcp_workflow():
    """
    演示MCP完整工作流程
    """
    print("\n" + "="*50)
    print("演示MCP完整工作流程")
    print("="*50)
    
    try:
        # Import the services directly
        from services.mcp_service import mcp_service
        from functions.master_service import master_service

        # 模拟实际使用场景
        print("场景：配置MCP服务器并使用工具")
        
        # 1. 注册MCP服务器
        print("\n1. 注册MCP服务器...")
        servers_to_register = [
            {"name": "web_search_server", "url": "http://search-service:8000"},
            {"name": "news_server", "url": "http://news-service:8000"},
            {"name": "weather_server", "url": "http://weather-service:8000"}
        ]
        
        for server_info in servers_to_register:
            result = master_service.call(
                "register_server", 
                server_name=server_info["name"], 
                server_url=server_info["url"]
            )
            print(f"  注册 {server_info['name']}: {'✓' if result else '✗'}")
        
        # 2. 获取所有工具用于智能推荐
        print("\n2. 获取所有工具用于智能推荐...")
        all_tools = master_service.get_all_tools_for_reranker()
        print(f"  可用工具数量: {len(all_tools)}")
        for tool in all_tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 3. 模拟根据用户问题选择最合适的工具并执行
        print("\n3. 工具选择与执行演示...")
        user_scenarios = [
            {
                "question": "今天北京的天气怎么样？",
                "expected_tool": "get_weather_info"
            },
            {
                "question": "给我找一些关于人工智能的最新新闻",
                "expected_tool": "fetch_news"
            },
            {
                "question": "你能告诉我量子计算是什么吗？",
                "expected_tool": "search_and_summarize"
            }
        ]
        
        for scenario in user_scenarios:
            print(f"\n  用户问题: {scenario['question']}")
            print(f"  推荐工具: {scenario['expected_tool']}")
            
            # 模拟工具执行
            result = master_service.execute_mcp_tool(
                scenario['expected_tool'],
                query=scenario['question']
            )
            
            if result["status"] == "success":
                print(f"  执行结果: ✓ 成功")
                if "result" in result:
                    print(f"    {result['result']}")
            else:
                print(f"  执行结果: ✗ 失败 - {result.get('error', '未知错误')}")
        
        # 4. 清理测试服务器
        print("\n4. 清理测试服务器...")
        for server_info in servers_to_register:
            result = master_service.call("unregister_server", server_name=server_info["name"])
            print(f"  注销 {server_info['name']}: {'✓' if result else '✗'}")
        
        print("\n" + "="*50)
        print("✓ MCP完整工作流程演示完成")

    except Exception as e:
        print(f"✗ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行核心功能测试
    test_mcp_functionality()
    
    # 运行完整工作流程演示
    demo_mcp_workflow()
