name: voice_chat_app
description: Real-time voice chat application with Flutter

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI
  cupertino_icons: ^1.0.2
  flutter_svg: ^2.0.7
  lottie: ^2.6.0
  
  # Audio
  flutter_sound: ^9.2.13  # Keeping the same version but will add macOS-specific config
  permission_handler: ^11.0.1
  
  # Network & WebSocket
  web_socket_channel: ^2.4.0
  http: ^1.1.0
  dio: ^5.3.2
  
  # State Management
  provider: ^6.0.5
  riverpod: ^2.4.6
  flutter_riverpod: ^2.4.6
  
  # Storage & Database
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  supabase_flutter: ^1.10.25
  
  # Utils
  uuid: ^4.1.0
  path_provider: ^2.1.1
  intl: ^0.18.1
  

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  mockito: ^5.4.2
  build_runner: ^2.4.6

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/
  
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700
