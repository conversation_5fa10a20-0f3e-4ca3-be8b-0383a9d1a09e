import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';
import '../config/app_config.dart';
import '../models/user_model.dart';

enum VoiceChatState {
  idle,
  connecting,
  connected,
  recording,
  processing,
  playing,
  error,
}

class VoiceChatMessage {
  final String type;
  final dynamic data;
  final DateTime timestamp;

  VoiceChatMessage({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  factory VoiceChatMessage.fromJson(Map<String, dynamic> json) {
    return VoiceChatMessage(
      type: json['type']?.toString() ?? 'unknown',
      data: json['data'] ?? json,
      timestamp: DateTime.now(),
    );
  }
}

final voiceChatServiceProvider = StateNotifierProvider<VoiceChatService, VoiceChatState>((ref) {
  return VoiceChatService();
});

class VoiceChatService extends StateNotifier<VoiceChatState> {
  VoiceChatService() : super(VoiceChatState.idle) {
    _initAudio();
  }

  WebSocketChannel? _channel;
  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;
  
  Timer? _recordingTimer;
  String? _currentSessionId;
  int? _currentNpcId;
  
  final StreamController<VoiceChatMessage> _messageController = StreamController.broadcast();
  final StreamController<String> _transcriptionController = StreamController.broadcast();
  final StreamController<Uint8List> _audioChunkController = StreamController.broadcast();
  
  Stream<VoiceChatMessage> get messageStream => _messageController.stream;
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<Uint8List> get audioChunkStream => _audioChunkController.stream;
  
  bool get isConnected => _channel != null;
  bool get isRecording => state == VoiceChatState.recording;
  bool get isPlaying => state == VoiceChatState.playing;
  String? get currentSessionId => _currentSessionId;

  Future<void> _initAudio() async {
    try {
      print('🎵 Initializing audio services...');
      
      // 尝试初始化flutter_sound
      _recorder = FlutterSoundRecorder();
      _player = FlutterSoundPlayer();
      
      // 在macOS上，flutter_sound可能有问题，所以我们用try-catch包装
      try {
        await _recorder!.openRecorder();
        print('✅ Recorder initialized');
      } catch (e) {
        print('⚠️ Recorder initialization failed: $e');
        _recorder = null;
      }
      
      try {
        await _player!.openPlayer();
        print('✅ Player initialized');
      } catch (e) {
        print('⚠️ Player initialization failed: $e');
        _player = null;
      }
      
      if (_recorder == null && _player == null) {
        print('⚠️ Audio services not available, will use simulated audio');
      } else {
        print('✅ Audio services partially or fully initialized');
      }
      
    } catch (e) {
      print('❌ Error initializing audio services: $e');
      _recorder = null;
      _player = null;
    }
  }

  Future<bool> requestPermissions() async {
    final microphoneStatus = await Permission.microphone.request();
    return microphoneStatus == PermissionStatus.granted;
  }

  Future<void> connect(UserModel user) async {
    if (state == VoiceChatState.connecting || isConnected) return;
    
    state = VoiceChatState.connecting;
    
    try {
      final uri = Uri.parse('${AppConfig.websocketUrl}/${user.id}');
      _channel = WebSocketChannel.connect(uri);
      
      // Listen to WebSocket messages
      _channel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );
      
      state = VoiceChatState.connected;
      _addMessage('system', 'Connected to voice chat service');
      
    } catch (e) {
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to connect: $e');
    }
  }

  Future<void> startSession(int npcId) async {
    if (!isConnected) return;
    
    _currentNpcId = npcId;
    
    final message = {
      'type': 'start_session',
      'npc_id': npcId,
    };
    
    _channel!.sink.add(json.encode(message));
    _addMessage('system', 'Starting conversation session...');
  }

  Future<void> startRecording() async {
    if (state != VoiceChatState.connected && state != VoiceChatState.idle) return;
    
    try {
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        _addMessage('error', 'Microphone permission denied');
        return;
      }

      if (_recorder != null) {
        try {
          await _recorder!.startRecorder(
            toFile: null, // Record to stream
            codec: Codec.pcm16,
            sampleRate: 16000,
            numChannels: 1,
          );
          
          state = VoiceChatState.recording;
          _addMessage('system', 'Recording started');
          
          // Start sending audio chunks (simplified for demo)
          _startAudioStreaming();
        } catch (e) {
          print('❌ Failed to start recorder: $e');
          // 降级到模拟录音
          state = VoiceChatState.recording;
          _addMessage('system', 'Recording started (simulated)');
          _startAudioStreaming();
        }
      } else {
        print('⚠️ Recorder not available, using simulated recording');
        // 降级到模拟录音
        state = VoiceChatState.recording;
        _addMessage('system', 'Recording started (simulated)');
        _startAudioStreaming();
      }
      
    } catch (e) {
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to start recording: $e');
    }
  }

  Future<void> stopRecording() async {
    if (state != VoiceChatState.recording) return;
    
    try {
      if (_recorder != null) {
        try {
          await _recorder!.stopRecorder();
        } catch (e) {
          print('❌ Failed to stop recorder: $e');
          // 继续处理，即使停止录音失败
        }
      }
      _recordingTimer?.cancel();
      
      state = VoiceChatState.processing;
      _addMessage('system', 'Processing audio...');
      
    } catch (e) {
      state = VoiceChatState.error;
      _addMessage('error', 'Failed to stop recording: $e');
    }
  }

  void _startAudioStreaming() {
    print('🎤 Starting audio streaming...');
    // Simplified audio streaming - in a real app you'd capture actual audio data
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) async {
      if (state != VoiceChatState.recording) {
        timer.cancel();
        print('🛑 Audio streaming stopped');
        return;
      }
      
      // For demo purposes, we'll send more realistic placeholder data
      // In a real implementation, you'd capture actual audio data
      // Generate some varying data to simulate audio
      final random = DateTime.now().microsecondsSinceEpoch % 256;
      final audioData = Uint8List.fromList([
        random & 0xFF,
        (random >> 1) & 0xFF,
        (random >> 2) & 0xFF,
        (random >> 3) & 0xFF,
      ]);
      
      print('📤 Sending audio chunk: ${audioData.length} bytes');
      _sendAudioChunk(audioData);
    });
  }

  void _sendAudioChunk(Uint8List audioData) {
    if (!isConnected) return;
    
    final base64Audio = base64.encode(audioData);
    final message = {
      'type': 'audio_chunk',
      'data': base64Audio,
    };
    
    _channel!.sink.add(json.encode(message));
  }

  void interrupt() {
    if (!isConnected) return;
    
    // Stop any current playback
    if (_player != null) {
      _player!.stopPlayer();
    }
    
    // Send interrupt signal
    final message = {'type': 'interrupt'};
    _channel!.sink.add(json.encode(message));
    
    state = VoiceChatState.connected;
    _addMessage('system', 'Assistant interrupted');
  }

  Future<void> endSession() async {
    if (!isConnected || _currentSessionId == null) return;
    
    final message = {'type': 'end_session'};
    _channel!.sink.add(json.encode(message));
    
    _currentSessionId = null;
    _currentNpcId = null;
    
    _addMessage('system', 'Session ended');
  }

  void _handleWebSocketMessage(dynamic message) {
    try {
      // The message might already be a Map (decoded JSON) or a String
      final data = message is String ? json.decode(message) : message;
      
      // Ensure data is a Map and handle potential type issues
      final Map<String, dynamic> messageData;
      if (data is Map<String, dynamic>) {
        messageData = data;
      } else if (data is Map) {
        messageData = Map<String, dynamic>.from(data);
      } else {
        // If data is not a map, create a default structure
        messageData = {
          'type': 'unknown',
          'data': data?.toString() ?? 'Unknown data',
        };
      }
      
      final chatMessage = VoiceChatMessage.fromJson(messageData);

      switch (chatMessage.type) {
        case 'session_started':
          // Handle potential type issues for session_id
          _currentSessionId = messageData['session_id']?.toString();
          _addMessage('system', 'Session started successfully');
          break;
          
        case 'transcription':
          final text = messageData['text'];
          final confidence = messageData['confidence'] ?? 1.0;
          _transcriptionController.add('$text (${(confidence * 100).toInt()}%)');
          _addMessage('transcription', text);
          break;

        case 'audio_chunk':
          _handleAudioChunk(messageData['data']);
          break;
          
        case 'response_complete':
          state = VoiceChatState.connected;
          _addMessage('system', 'Response complete');
          break;
          
        case 'interrupted':
          state = VoiceChatState.connected;
          _addMessage('system', 'Assistant was interrupted');
          break;
          
        case 'session_ended':
          _currentSessionId = null;
          _addMessage('system', 'Session ended');
          break;
          
        case 'error':
          state = VoiceChatState.error;
          _addMessage('error', messageData['message'] ?? 'Unknown error');
          break;

        default:
          _addMessage('unknown', messageData);
      }
      
      _messageController.add(chatMessage);
      
    } catch (e) {
      print('Error handling WebSocket message: $e');
    }
  }

  void _handleAudioChunk(String base64Audio) {
    try {
      final audioData = base64.decode(base64Audio);
      _audioChunkController.add(audioData);
      
      // Play audio chunk
      _playAudioChunk(audioData);
      
    } catch (e) {
      print('Error handling audio chunk: $e');
    }
  }

  Future<void> _playAudioChunk(Uint8List audioData) async {
    try {
      if (state != VoiceChatState.playing) {
        state = VoiceChatState.playing;
      }
      
      // For demo purposes, we'll just simulate audio playback
      // In a real implementation, you'd stream the audio data to the player
      await Future.delayed(const Duration(milliseconds: 100));
      
    } catch (e) {
      print('Error playing audio chunk: $e');
    }
  }

  void _handleWebSocketError(error) {
    state = VoiceChatState.error;
    _addMessage('error', 'WebSocket error: $error');
  }

  void _handleWebSocketClosed() {
    state = VoiceChatState.idle;
    _addMessage('system', 'Connection closed');
    _cleanup();
  }

  void _addMessage(String type, dynamic data) {
    final message = VoiceChatMessage(
      type: type,
      data: data,
      timestamp: DateTime.now(),
    );
    _messageController.add(message);
  }

  void _cleanup() {
    _recordingTimer?.cancel();
    if (_recorder != null) {
      _recorder!.stopRecorder();
    }
    if (_player != null) {
      _player!.stopPlayer();
    }
    _currentSessionId = null;
    _currentNpcId = null;
  }

  void disconnect() {
    _channel?.sink.close();
    _channel = null;
    _cleanup();
    state = VoiceChatState.idle;
  }

  @override
  void dispose() {
    disconnect();
    _messageController.close();
    _transcriptionController.close();
    _audioChunkController.close();
    
    // 安全地关闭音频服务
    if (_recorder != null) {
      try {
        _recorder!.closeRecorder();
      } catch (e) {
        print('⚠️ Error closing recorder: $e');
      }
    }
    if (_player != null) {
      try {
        _player!.closePlayer();
      } catch (e) {
        print('⚠️ Error closing player: $e');
      }
    }
    super.dispose();
  }
}
