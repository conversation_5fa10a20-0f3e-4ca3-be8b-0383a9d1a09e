import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/user_model.dart';
import '../services/memory_auth_service.dart';
import '../config/app_config.dart';
import 'voice_chat_screen.dart';

final npcListProvider = FutureProvider<List<NPCModel>>((ref) async {
  // Try to fetch from backend first, fallback to demo NPCs
  try {
    final response = await http.get(Uri.parse('${AppConfig.backendUrl}/npcs'));
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final List<dynamic> npcsJson = data['npcs'];
      return npcsJson.map((json) => NPCModel.fromJson(json)).toList();
    }
  } catch (e) {
    // Backend not available, use demo NPCs
  }
  
  // Return demo NPCs including Rigo
  return [
    NPCModel.createRigo(),
    NPCModel(
      id: 1,
      name: 'Assistant',
      description: 'A helpful AI assistant for general conversations',
      systemPrompt: 'You are a helpful AI assistant.',
      avatarUrl: null,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    NPCModel(
      id: 2,
      name: 'Friend',
      description: 'A casual friend for friendly chats',
      systemPrompt: 'You are a friendly companion.',
      avatarUrl: null,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];
});

class NPCSelectionScreen extends ConsumerStatefulWidget {
  const NPCSelectionScreen({super.key});

  @override
  ConsumerState<NPCSelectionScreen> createState() => _NPCSelectionScreenState();
}

class _NPCSelectionScreenState extends ConsumerState<NPCSelectionScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  NPCModel? _selectedNPC;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startChat() {
    if (_selectedNPC != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => VoiceChatScreen(npc: _selectedNPC!),
        ),
      );
    }
  }

  void _logout() async {
    final authService = ref.read(memoryAuthServiceProvider.notifier);
    await authService.logout();
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    final npcListAsync = ref.watch(npcListProvider);
    final authState = ref.watch(memoryAuthServiceProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose Your Assistant'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Logout',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // User Info Header
            _buildUserHeader(authState),
            
            // NPC List
            Expanded(
              child: npcListAsync.when(
                data: (npcs) => _buildNPCList(npcs),
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stack) => _buildErrorWidget(error),
              ),
            ),
            
            // Start Chat Button
            _buildStartChatButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildUserHeader(AsyncValue<UserModel?> authState) {
    return authState.when(
      data: (user) => Container(
        padding: const EdgeInsets.all(AppConfig.padding),
        margin: const EdgeInsets.all(AppConfig.padding),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primary.withOpacity(0.1),
              Theme.of(context).colorScheme.secondary.withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundImage: user?.avatarUrl != null
                  ? NetworkImage(user!.avatarUrl!)
                  : null,
              child: user?.avatarUrl == null
                  ? const Icon(Icons.person, size: 30)
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    user?.nickname ?? 'Guest User',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildNPCList(List<NPCModel> npcs) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConfig.padding),
      itemCount: npcs.length,
      itemBuilder: (context, index) {
        final npc = npcs[index];
        final isSelected = _selectedNPC?.id == npc.id;
        
        return AnimatedContainer(
          duration: AppConfig.animationDuration,
          margin: const EdgeInsets.only(bottom: 12),
          child: Material(
            elevation: isSelected ? 8 : 2,
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            child: InkWell(
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              onTap: () {
                setState(() {
                  _selectedNPC = npc;
                });
              },
              child: Container(
                padding: const EdgeInsets.all(AppConfig.padding),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Colors.transparent,
                    width: 2,
                  ),
                  gradient: isSelected
                      ? LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.primary.withOpacity(0.1),
                            Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                          ],
                        )
                      : null,
                ),
                child: Row(
                  children: [
                    // NPC Avatar
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: npc.avatarUrl != null
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(30),
                              child: Image.network(
                                npc.avatarUrl!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    const Icon(Icons.smart_toy, size: 30),
                              ),
                            )
                          : const Icon(Icons.smart_toy, size: 30),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // NPC Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            npc.name,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : null,
                            ),
                          ),
                          if (npc.description != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              npc.description!,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Colors.grey[600],
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                    ),
                    
                    // Selection Indicator
                    if (isSelected)
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorWidget(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load assistants',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => ref.refresh(npcListProvider),
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildStartChatButton() {
    return Container(
      padding: const EdgeInsets.all(AppConfig.padding),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _selectedNPC != null ? _startChat : null,
          icon: const Icon(Icons.chat),
          label: const Text(
            'Start Voice Chat',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            ),
            elevation: 4,
          ),
        ),
      ),
    );
  }
}