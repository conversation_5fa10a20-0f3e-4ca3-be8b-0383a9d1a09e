import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../models/user_model.dart';
import '../services/memory_auth_service.dart';
import '../services/voice_chat_service.dart';

class VoiceChatScreen extends ConsumerStatefulWidget {
  final NPCModel npc;

  const VoiceChatScreen({
    super.key,
    required this.npc,
  });

  @override
  ConsumerState<VoiceChatScreen> createState() => _VoiceChatScreenState();
}

class _VoiceChatScreenState extends ConsumerState<VoiceChatScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;

  StreamSubscription? _messageSubscription;
  StreamSubscription? _transcriptionSubscription;

  String _currentTranscription = '';
  String _statusMessage = 'Tap to start talking';

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeVoiceChat();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.easeOut,
    ));
  }

  Future<void> _initializeVoiceChat() async {
    // 延迟到下一帧执行，避免在initState中直接调用provider
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final voiceChatService = ref.read(voiceChatServiceProvider.notifier);
        final authState = ref.read(memoryAuthServiceProvider);

        await authState.when(
          data: (user) async {
            if (user != null) {
              setState(() {
                _statusMessage = 'Connecting...';
              });
              
              try {
                await voiceChatService.connect(user);
                print('✅ WebSocket connected for user: ${user.id}');
                
                await voiceChatService.startSession(widget.npc.id);
                print('✅ Session started for NPC: ${widget.npc.id}');
                
                if (mounted) {
                  setState(() {
                    _statusMessage = 'Ready to chat';
                  });
                }
              } catch (e) {
                print('❌ Connection error: $e');
                if (mounted) {
                  setState(() {
                    _statusMessage = 'Connection error: ${e.toString()}';
                  });
                }
              }
            }
          },
          loading: () {
            setState(() {
              _statusMessage = 'Loading...';
            });
          },
          error: (error, stack) {
            setState(() {
              _statusMessage = 'Connection failed';
            });
          },
        );

        // Listen to voice chat messages
        _messageSubscription = voiceChatService.messageStream.listen((message) {
          if (mounted) {
            setState(() {
              // 根据消息类型更新状态
              switch (message.type) {
                case 'response_complete':
                  _statusMessage = 'Tap to start talking';
                  break;
                case 'system':
                  if (message.data.toString().contains('Session started')) {
                    _statusMessage = 'Ready to chat';
                  }
                  break;
                case 'error':
                  _statusMessage = 'Error: ${message.data}';
                  break;
              }
            });
          }
        });

        // Listen to transcriptions
        _transcriptionSubscription = voiceChatService.transcriptionStream.listen((text) {
          if (mounted) {
            setState(() {
              _currentTranscription = text;
            });
          }
        });
      } catch (e) {
        if (mounted) {
          setState(() {
            _statusMessage = 'Failed to initialize: ${e.toString()}';
          });
        }
      }
    });
  }

  void _startRecording() {
    try {
      print('🎤 Starting recording...');
      final voiceChatService = ref.read(voiceChatServiceProvider.notifier);
      voiceChatService.startRecording();
      
      _pulseController.repeat(reverse: true);
      _waveController.repeat();
      
      setState(() {
        _statusMessage = 'Listening...';
        _currentTranscription = '';
      });
      print('✅ Recording started');
    } catch (e) {
      print('❌ Failed to start recording: $e');
      setState(() {
        _statusMessage = 'Failed to start recording: ${e.toString()}';
      });
    }
  }

  void _stopRecording() {
    try {
      print('🛑 Stopping recording...');
      final voiceChatService = ref.read(voiceChatServiceProvider.notifier);
      voiceChatService.stopRecording();
      
      _pulseController.stop();
      _waveController.stop();
      
      setState(() {
        _statusMessage = 'Processing...';
      });
      print('✅ Recording stopped, processing...');
      
      // 设置一个超时，如果10秒后还在Processing状态，就重置
      Timer(const Duration(seconds: 10), () {
        if (mounted && _statusMessage == 'Processing...') {
          print('⏰ Processing timeout, resetting to ready state');
          setState(() {
            _statusMessage = 'Tap to start talking';
          });
        }
      });
    } catch (e) {
      print('❌ Failed to stop recording: $e');
      setState(() {
        _statusMessage = 'Failed to stop recording: ${e.toString()}';
      });
    }
  }

  void _endSession() async {
    final voiceChatService = ref.read(voiceChatServiceProvider.notifier);
    await voiceChatService.endSession();
    voiceChatService.disconnect();
    
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    _messageSubscription?.cancel();
    _transcriptionSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final voiceChatState = ref.watch(voiceChatServiceProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      body: SafeArea(
        child: Column(
          children: [
            // Custom App Bar
            _buildAppBar(),
            
            // Main Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    
                    // NPC Avatar Section
                    _buildNPCSection(),
                    
                    const Spacer(flex: 2),
                    
                    // Voice Visualization
                    _buildVoiceVisualization(voiceChatState),
                    
                    const SizedBox(height: 20),
                    
                    // Status Message
                    _buildStatusMessage(),
                    
                    // Transcription Section
                    if (_currentTranscription.isNotEmpty)
                      _buildTranscriptionSection(),
                    
                    const Spacer(flex: 1),
                    
                    // Control Button
                    _buildControlButton(voiceChatState),
                    
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
            onPressed: _endSession,
          ),
          Expanded(
            child: Text(
              widget.npc.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.red.withOpacity(0.3)),
            ),
            child: const Text(
              'BETA',
              style: TextStyle(
                color: Colors.red,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNPCSection() {
    return Column(
      children: [
        // NPC Avatar with glow effect
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.3),
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFF1A1A1A),
              borderRadius: BorderRadius.circular(50),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: widget.npc.avatarUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(48),
                    child: Image.network(
                      widget.npc.avatarUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                        const Icon(Icons.smart_toy, size: 40, color: Colors.white),
                    ),
                  )
                : const Icon(Icons.smart_toy, size: 40, color: Colors.white),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // NPC Name
        Text(
          widget.npc.name,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 22,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildVoiceVisualization(VoiceChatState state) {
    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Outer ripple effect
          if (state == VoiceChatState.recording)
            AnimatedBuilder(
              animation: _waveAnimation,
              builder: (context, child) {
                return Container(
                  width: 200 + (_waveAnimation.value * 100),
                  height: 200 + (_waveAnimation.value * 100),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(150),
                    border: Border.all(
                      color: Colors.blue.withOpacity(0.3 - (_waveAnimation.value * 0.3)),
                      width: 2,
                    ),
                  ),
                );
              },
            ),
          
          // Middle ripple
          if (state == VoiceChatState.recording)
            AnimatedBuilder(
              animation: _waveAnimation,
              builder: (context, child) {
                return Container(
                  width: 160 + (_waveAnimation.value * 60),
                  height: 160 + (_waveAnimation.value * 60),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(110),
                    border: Border.all(
                      color: Colors.blue.withOpacity(0.5 - (_waveAnimation.value * 0.4)),
                      width: 1,
                    ),
                  ),
                );
              },
            ),
          
          // Main voice button
          GestureDetector(
            onTapDown: (_) => _startRecording(),
            onTapUp: (_) => _stopRecording(),
            onTapCancel: () => _stopRecording(),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: state == VoiceChatState.recording ? 140 : 120,
              height: state == VoiceChatState.recording ? 140 : 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: state == VoiceChatState.recording
                      ? [Colors.red.withOpacity(0.8), Colors.red.withOpacity(0.6)]
                      : [Colors.blue.withOpacity(0.8), Colors.blue.withOpacity(0.6)],
                ),
                borderRadius: BorderRadius.circular(70),
                boxShadow: [
                  BoxShadow(
                    color: (state == VoiceChatState.recording ? Colors.red : Colors.blue)
                        .withOpacity(0.4),
                    blurRadius: 20,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Icon(
                state == VoiceChatState.recording ? Icons.stop : Icons.mic,
                size: state == VoiceChatState.recording ? 50 : 45,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusMessage() {
    return Container(
      height: 40,
      alignment: Alignment.center,
      child: Text(
        _statusMessage,
        style: TextStyle(
          color: Colors.white.withOpacity(0.8),
          fontSize: 16,
          fontWeight: FontWeight.w400,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTranscriptionSection() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 80),
      margin: const EdgeInsets.only(top: 10),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
      ),
      child: SingleChildScrollView(
        child: Text(
          _currentTranscription,
          style: TextStyle(
            color: Colors.white.withOpacity(0.9),
            fontSize: 14,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildControlButton(VoiceChatState state) {
    bool isRecording = state == VoiceChatState.recording;
    
    return GestureDetector(
      onTapDown: (_) => _startRecording(),
      onTapUp: (_) => _stopRecording(),
      onTapCancel: () => _stopRecording(),
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isRecording
                ? [Colors.red.withOpacity(0.8), Colors.red.withOpacity(0.6)]
                : [Colors.blue.withOpacity(0.8), Colors.blue.withOpacity(0.6)],
          ),
          boxShadow: [
            BoxShadow(
              color: (isRecording ? Colors.red : Colors.blue).withOpacity(0.4),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Icon(
          isRecording ? Icons.stop : Icons.mic,
          size: 35,
          color: Colors.white,
        ),
      ),
    );
  }
}