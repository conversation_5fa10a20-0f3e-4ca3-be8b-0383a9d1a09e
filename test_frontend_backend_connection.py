#!/usr/bin/env python3
"""
测试前端到后端的连接
"""
import requests
import json

def test_connection():
    """测试前端到后端的连接"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试前端到后端的连接...")
    
    # 1. 测试基本连接
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ 基本连接: {response.status_code}")
        if response.status_code == 200:
            print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"❌ 基本连接失败: {e}")
        return False
    
    # 2. 测试登录API（使用查询参数）
    try:
        response = requests.post(
            f"{base_url}/auth/login",
            params={
                "username": "test_user",
                "password": "test_password"
            },
            timeout=10
        )
        print(f"✅ 登录API: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   用户: {result.get('user', {})}")
            print(f"   Token: {result.get('token', 'N/A')}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"❌ 登录API失败: {e}")
    
    # 3. 测试CORS预检请求
    try:
        response = requests.options(
            f"{base_url}/auth/login",
            headers={
                'Origin': 'http://localhost',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            },
            timeout=5
        )
        print(f"✅ CORS预检: {response.status_code}")
        print(f"   CORS Headers: {dict(response.headers)}")
    except Exception as e:
        print(f"❌ CORS预检失败: {e}")
    
    return True

if __name__ == "__main__":
    test_connection()