import sys
import os
import json
import unittest
from unittest.mock import patch, mock_open
import tempfile
from pathlib import Path

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.mcp_service import MCPService

class TestMCPService(unittest.TestCase):
    """
    MCPService的测试用例
    """
    
    def setUp(self):
        """
        测试前的准备工作
        """
        # 创建临时配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, "multi_server_config.json")
        self.mcp_service = MCPService(self.config_path)
    
    def tearDown(self):
        """
        测试后的清理工作
        """
        # 清理临时文件
        if os.path.exists(self.config_path):
            os.remove(self.config_path)
        os.rmdir(self.temp_dir)
    
    def test_get_all_tools(self):
        """
        测试获取所有工具的功能
        """
        tools = self.mcp_service.get_all_tools()
        
        # 验证返回结果不为空
        self.assertIsNotNone(tools)
        self.assertIsInstance(tools, list)
        self.assertGreater(len(tools), 0)
        
        # 验证每个工具都有name和description字段
        for tool in tools:
            self.assertIn("name", tool)
            self.assertIn("description", tool)
            self.assertIsInstance(tool["name"], str)
            self.assertIsInstance(tool["description"], str)
    
    def test_initialize_config(self):
        """
        测试初始化配置文件功能
        """
        # 准备测试配置数据
        test_config = {
            "servers": [
                {
                    "name": "test_server",
                    "url": "http://test.server.com",
                    "tools": ["tool1", "tool2"]
                }
            ],
            "default_server": "test_server"
        }
        
        # 执行初始化
        result = self.mcp_service.initialize_config(test_config)
        
        # 验证初始化成功
        self.assertTrue(result)
        
        # 验证配置已更新
        self.assertEqual(self.mcp_service.config, test_config)
        
        # 验证文件已创建
        self.assertTrue(os.path.exists(self.config_path))
        
        # 验证文件内容正确
        with open(self.config_path, 'r', encoding='utf-8') as f:
            saved_config = json.load(f)
        self.assertEqual(saved_config, test_config)
    
    def test_delete_config(self):
        """
        测试删除配置文件功能
        """
        # 先创建一个配置文件
        test_config = {
            "servers": [],
            "default_server": None
        }
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(test_config, f)
        
        # 验证文件存在
        self.assertTrue(os.path.exists(self.config_path))
        
        # 执行删除
        result = self.mcp_service.delete_config()
        
        # 验证删除成功
        self.assertTrue(result)
        
        # 验证文件已被删除
        self.assertFalse(os.path.exists(self.config_path))
        
        # 验证配置已重置为默认值
        expected_default = {
            "servers": [],
            "default_server": None
        }
        self.assertEqual(self.mcp_service.config, expected_default)
    
    def test_delete_config_nonexistent(self):
        """
        测试删除不存在的配置文件
        """
        # 确保文件不存在
        if os.path.exists(self.config_path):
            os.remove(self.config_path)
        
        # 执行删除
        result = self.mcp_service.delete_config()
        
        # 验证删除成功（即使文件不存在）
        self.assertTrue(result)
    
    def test_execute_tool_success(self):
        """
        测试成功执行工具
        """
        # 测试执行search_and_summarize工具
        result = self.mcp_service.execute_tool(
            "search_and_summarize", 
            query="人工智能发展趋势"
        )
        
        # 验证返回结果结构
        self.assertIn("tool_name", result)
        self.assertIn("parameters", result)
        self.assertIn("status", result)
        self.assertIn("timestamp", result)
        self.assertIn("result", result)
        
        # 验证具体值
        self.assertEqual(result["tool_name"], "search_and_summarize")
        self.assertEqual(result["parameters"]["query"], "人工智能发展趋势")
        self.assertEqual(result["status"], "success")
        self.assertIsNotNone(result["timestamp"])
        self.assertIn("summary", result["result"])
    
    def test_execute_tool_with_different_parameters(self):
        """
        测试执行不同参数的工具
        """
        # 测试执行fetch_news工具
        result = self.mcp_service.execute_tool(
            "fetch_news", 
            category="technology",
            limit=5
        )
        
        # 验证参数正确传递
        self.assertEqual(result["tool_name"], "fetch_news")
        self.assertEqual(result["parameters"]["category"], "technology")
        self.assertEqual(result["parameters"]["limit"], 5)
        self.assertIn("news", result["result"])
    
    def test_execute_unknown_tool(self):
        """
        测试执行未知工具
        """
        result = self.mcp_service.execute_tool(
            "unknown_tool", 
            param1="value1"
        )
        
        # 验证工具仍能执行（返回通用结果）
        self.assertEqual(result["tool_name"], "unknown_tool")
        self.assertEqual(result["status"], "success")
        self.assertIn("message", result["result"])
    
    @patch("builtins.open", new_callable=mock_open)
    @patch("os.path.exists")
    def test_load_config_file_error(self, mock_exists, mock_file):
        """
        测试加载配置文件时发生错误
        """
        # 模拟文件存在但读取时出错
        mock_exists.return_value = True
        mock_file.side_effect = Exception("读取文件错误")
        
        # 重新初始化服务以触发配置加载
        service = MCPService(self.config_path)
        
        # 验证使用默认配置
        expected_default = {
            "servers": [],
            "default_server": None
        }
        self.assertEqual(service.config, expected_default)
    
    @patch("builtins.open", new_callable=mock_open)
    @patch("os.path.exists")
    @patch("pathlib.Path.mkdir")
    def test_save_config_file_error(self, mock_mkdir, mock_exists, mock_file):
        """
        测试保存配置文件时发生错误
        """
        # 模拟文件保存时出错
        mock_exists.return_value = False
        mock_file.side_effect = Exception("保存文件错误")
        mock_mkdir.return_value = None
        
        # 尝试初始化配置
        test_config = {"servers": [], "default_server": None}
        result = self.mcp_service.initialize_config(test_config)
        
        # 验证保存失败
        self.assertFalse(result)

def test_mcp_service_integration():
    """
    测试MCPService与RerankerService的集成
    """
    print("开始测试MCPService与RerankerService的集成...")
    
    try:
        # 由于需要DASHSCOPE_API_KEY，我们只测试基本功能
        from utils.reranker_service import RerankerService
        
        # 初始化MCP服务
        mcp_service = MCPService()
        print("✓ MCP服务初始化成功")
        
        # 获取所有工具
        tools = mcp_service.get_all_tools()
        print(f"✓ 获取到 {len(tools)} 个工具")
        
        # 验证工具格式
        for tool in tools:
            assert "name" in tool, "工具缺少name字段"
            assert "description" in tool, "工具缺少description字段"
        
        print("✓ 工具格式验证通过")
        
        # 测试配置文件操作
        test_config = {
            "servers": [
                {
                    "name": "integration_test_server",
                    "url": "http://test.local",
                    "tools": ["test_tool"]
                }
            ],
            "default_server": "integration_test_server"
        }
        
        # 初始化配置
        init_result = mcp_service.initialize_config(test_config)
        assert init_result, "配置初始化失败"
        print("✓ 配置初始化成功")
        
        # 删除配置
        delete_result = mcp_service.delete_config()
        assert delete_result, "配置删除失败"
        print("✓ 配置删除成功")
        
        # 测试工具执行
        execution_result = mcp_service.execute_tool("test_tool", param="test_value")
        assert execution_result["status"] == "success", "工具执行失败"
        print("✓ 工具执行成功")
        
        print("\n✓ 集成测试完成")
        
    except Exception as e:
        print(f"✗ 集成测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "="*50)
    
    # 运行集成测试
    test_mcp_service_integration()
