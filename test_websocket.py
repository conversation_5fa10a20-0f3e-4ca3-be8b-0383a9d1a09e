import asyncio
import websockets
import json

async def test_websocket():
    uri = "ws://localhost:8001/ws/1"  # 使用用户ID 1进行测试
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket 连接成功")
            
            # 发送 start_session 消息
            start_session_msg = {
                "type": "start_session",
                "npc_id": 1
            }
            await websocket.send(json.dumps(start_session_msg))
            print("📤 发送 start_session 消息")
            
            # 等待响应
            response = await websocket.recv()
            response_data = json.loads(response)
            print(f"📥 收到响应: {response}")
            print(f"📋 解析后的响应: {response_data}")
            
            if response_data.get("type") == "session_started":
                print("✅ 会话创建成功")
                print(f"📋 会话ID: {response_data.get('session_id')}")
                print(f"📋 NPC ID: {response_data.get('npc_id')}")
            elif response_data.get("type") == "error":
                print(f"❌ 会话创建失败: {response_data.get('message')}")
            else:
                print(f"❓ 收到未知响应类型: {response_data.get('type')}")
                
    except Exception as e:
        print(f"❌ WebSocket 连接失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket())
