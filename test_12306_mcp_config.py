import sys
import os
import json

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_12306_mcp_config():
    """
    测试使用12306-mcp.json配置文件
    """
    print("开始测试12306 MCP配置...")
    
    try:
        # Import the services directly
        from services.mcp_service import mcp_service
        from functions.master_service import master_service

        # 1. 读取12306-mcp.json配置文件
        print("\n1. 读取12306-mcp.json配置文件...")
        config_path = os.path.join(os.path.dirname(__file__), 'backend', 'mcp_config', '12306-mcp.json')
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        print(f"✓ 成功读取配置文件")
        print(f"  配置内容: {json.dumps(config_data, ensure_ascii=False, indent=2)}")
        
        # 2. 解析配置并注册服务器
        print("\n2. 解析配置并注册服务器...")
        mcp_servers = config_data.get("mcpServers", {})
        
        for server_name, server_config in mcp_servers.items():
            command = server_config.get("command")
            args = server_config.get("args", [])
            
            print(f"  解析服务器: {server_name}")
            print(f"    命令: {command}")
            print(f"    参数: {args}")
            
            # 对于本地命令型MCP服务器，我们可以创建一个特殊的URL表示
            # 在实际实现中，这可能需要通过进程管理来启动
            server_url = f"local://{command} {' '.join(args)}"
            
            # 注册服务器
            result = master_service.call(
                "register_server", 
                server_name=server_name, 
                server_url=server_url,
                server_info={
                    "command": command,
                    "args": args,
                    "type": "local_command"
                }
            )
            print(f"  ✓ 服务器注册: {'成功' if result else '失败'}")
        
        # 3. 列出所有已注册的服务器
        print("\n3. 当前已注册的服务器:")
        servers = master_service.call("list_servers")
        for server in servers:
            print(f"  - {server['name']}: {server['url']}")
            if 'command' in server:
                print(f"    命令: {server['command']} {server.get('args', [])}")
        
        # 4. 获取所有工具用于reranker
        print("\n4. 获取所有工具用于reranker...")
        all_tools = master_service.get_all_tools_for_reranker()
        print(f"✓ 成功获取到 {len(all_tools)} 个工具用于reranker")
        for tool in all_tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 5. 模拟执行工具
        print("\n5. 模拟执行工具...")
        if all_tools:
            # 选择第一个工具进行测试执行
            test_tool = all_tools[0]
            print(f"  测试执行工具: {test_tool['name']}")
            
            result = master_service.execute_mcp_tool(
                test_tool['name'],
                server_name="12306-mcp" if "12306-mcp" in [s["name"] for s in servers] else None,
                query="查询北京到上海的火车票"
            )
            
            if result["status"] == "success":
                print(f"  ✓ 工具执行成功")
                print(f"    执行时间: {result['timestamp']}")
                if "result" in result:
                    result_summary = str(result['result'])[:100] + "..." if len(str(result['result'])) > 100 else str(result['result'])
                    print(f"    结果摘要: {result_summary}")
            else:
                print(f"  ✗ 工具执行失败: {result.get('error', '未知错误')}")
        else:
            print("  没有可用工具进行测试")
        
        # 6. 测试配置文件操作
        print("\n6. 测试配置文件操作...")
        
        # 创建一个新的配置示例
        new_config = {
            "servers": [
                {
                    "name": "12306_test_server",
                    "url": "local://npx -y 12306-mcp",
                    "command": "npx",
                    "args": ["-y", "12306-mcp"],
                    "type": "local_command"
                }
            ],
            "default_server": "12306_test_server"
        }
        
        # 初始化配置
        init_result = master_service.initialize_mcp_config(new_config)
        print(f"✓ 配置初始化: {'成功' if init_result else '失败'}")
        
        # 验证配置
        servers_after_init = master_service.call("list_servers")
        print(f"  初始化后服务器数量: {len(servers_after_init)}")
        
        # 删除配置
        delete_result = master_service.delete_mcp_config()
        print(f"✓ 配置删除: {'成功' if delete_result else '失败'}")
        
        print("\n" + "="*50)
        print("✓ 12306 MCP配置测试完成")
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def demo_12306_workflow():
    """
    演示12306 MCP工作流程
    """
    print("\n" + "="*50)
    print("演示12306 MCP工作流程")
    print("="*50)
    
    try:
        # Import the services directly
        from services.mcp_service import mcp_service
        from functions.master_service import master_service

        # 模拟实际使用场景：12306火车票查询系统
        print("场景：配置12306 MCP服务器并查询火车票信息")
        
        # 1. 从12306-mcp.json文件加载配置
        print("\n1. 加载12306 MCP配置...")
        config_path = os.path.join(os.path.dirname(__file__), 'backend', 'mcp_config', '12306-mcp.json')
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 2. 注册12306 MCP服务器
        print("\n2. 注册12306 MCP服务器...")
        mcp_servers = config_data.get("mcpServers", {})
        
        for server_name, server_config in mcp_servers.items():
            command = server_config.get("command")
            args = server_config.get("args", [])
            server_url = f"local://{command} {' '.join(args)}"
            
            result = master_service.call(
                "register_server", 
                server_name=server_name, 
                server_url=server_url,
                server_info={
                    "command": command,
                    "args": args,
                    "type": "local_command"
                }
            )
            print(f"  注册 {server_name}: {'✓' if result else '✗'}")
        
        # 3. 查看已注册的服务器
        print("\n3. 当前已注册的服务器:")
        servers = master_service.call("list_servers")
        for server in servers:
            print(f"  - {server['name']}: {server['url']}")
        
        # 4. 获取服务器工具信息
        print("\n4. 获取12306服务器工具信息...")
        for server in servers:
            if server['name'] == '12306-mcp':
                tools = master_service.call("get_server_tools", server_name=server['name'])
                print(f"  {server['name']} 提供的工具:")
                if tools:
                    for tool in tools:
                        print(f"    - {tool['name']}: {tool['description']}")
                else:
                    print("    - 暂无工具信息（模拟数据）")
                    # 模拟一些12306可能提供的工具
                    mock_tools = [
                        {"name": "query_train_schedule", "description": "查询列车时刻表和余票信息"},
                        {"name": "book_ticket", "description": "预订火车票"},
                        {"name": "cancel_booking", "description": "取消预订"},
                        {"name": "query_station_info", "description": "查询车站信息"}
                    ]
                    for tool in mock_tools:
                        print(f"    - {tool['name']}: {tool['description']}")
        
        # 5. 获取所有工具用于智能推荐
        print("\n5. 获取所有工具用于智能推荐...")
        all_tools = master_service.get_all_tools_for_reranker()
        print(f"  可用工具数量: {len(all_tools)}")
        for tool in all_tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 6. 模拟用户查询火车票信息
        print("\n6. 用户查询处理演示...")
        user_queries = [
            "我想查询明天北京到上海的高铁",
            "帮我预订后天从广州到深圳的火车票",
            "查询一下南京站的信息"
        ]
        
        # 模拟工具推荐（实际应该使用reranker服务）
        tool_mapping = {
            "查询明天北京到上海的高铁": "query_train_schedule",
            "帮我预订后天从广州到深圳的火车票": "book_ticket",
            "查询一下南京站的信息": "query_station_info"
        }
        
        for query in user_queries:
            print(f"\n  用户查询: {query}")
            recommended_tool = tool_mapping.get(query, "query_train_schedule")
            print(f"  推荐工具: {recommended_tool}")
            
            # 模拟工具执行
            result = master_service.execute_mcp_tool(
                recommended_tool,
                query=query
            )
            
            if result["status"] == "success":
                print(f"  执行结果: ✓ 成功")
                if "result" in result:
                    print(f"    {result['result']}")
            else:
                print(f"  执行结果: ✗ 失败 - {result.get('error', '未知错误')}")
        
        # 7. 清理（在实际应用中可能不需要）
        print("\n7. 清理服务器...")
        for server in servers:
            result = master_service.call("unregister_server", server_name=server['name'])
            print(f"  注销 {server['name']}: {'✓' if result else '✗'}")
        
        print("\n" + "="*50)
        print("✓ 12306 MCP工作流程演示完成")

    except Exception as e:
        print(f"✗ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行12306配置测试
    test_12306_mcp_config()
    
    # 运行12306工作流程演示
    demo_12306_workflow()
