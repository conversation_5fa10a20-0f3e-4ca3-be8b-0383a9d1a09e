#!/usr/bin/env python3
"""
启动整个语音聊天应用的脚本
包括后端服务和前端应用启动说明
"""
import os
import sys
import subprocess
import signal
import time
from threading import Thread

# Global variable to track backend process
backend_process = None

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully"""
    print("\n\n⏹️  正在停止应用...")
    if backend_process:
        backend_process.terminate()
        print("✅ 后端服务已停止")
    print("👋 应用已完全停止")
    sys.exit(0)

def start_backend():
    """Start the backend service"""
    global backend_process
    
    print("🚀 正在启动后端服务...")
    print("   服务地址: http://localhost:8000")
    print("   API文档: http://localhost:8000/docs")
    print("   按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        # Change to backend directory
        backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
        os.chdir(backend_dir)
        
        # Start backend service
        backend_process = subprocess.Popen([sys.executable, "main.py"])
        
        # Wait a moment for the server to start
        time.sleep(3)
        
        print("✅ 后端服务启动成功!")
        print("   健康检查: curl http://localhost:8000/health")
        print("   测试端点: curl http://localhost:8000/test")
        print("-" * 50)
        
        # Keep the process running
        backend_process.wait()
        
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return False
    except KeyboardInterrupt:
        pass
    
    return True

def show_frontend_instructions():
    """Show instructions for starting the frontend"""
    print("\n📱 前端应用启动说明")
    print("=" * 50)
    
    print("\n📋 前端应用信息:")
    print("   应用名称: Voice Chat App")
    print("   开发框架: Flutter")
    print("   主入口文件: frontend/lib/main.dart")
    
    print("\n⚙️  启动步骤:")
    print("   1. 打开新的终端窗口")
    print("   2. 确保已安装 Flutter SDK")
    print("   3. 安装项目依赖:")
    print("      cd frontend")
    print("      flutter pub get")
    print("   4. 启动开发服务器:")
    print("      flutter run")
    
    print("\n💡 提示:")
    print("   - 确保后端服务已在运行 (http://localhost:8000)")
    print("   - 前端配置文件: frontend/lib/config/app_config.dart")
    print("   - 默认后端地址: http://localhost:8000")
    
    print("\n🔗 相关链接:")
    print("   - 后端API文档: http://localhost:8000/docs")
    print("   - Flutter文档: https://flutter.dev/docs")
    print("   - 项目文档: frontend/README.md")
    
    print("\n" + "=" * 50)

def main():
    """Main function to start the complete application"""
    print("🎯 语音聊天应用启动器")
    print("=" * 50)
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start backend in a separate thread
        backend_thread = Thread(target=start_backend)
        backend_thread.daemon = True
        backend_thread.start()
        
        # Give backend some time to start
        time.sleep(5)
        
        # Show frontend instructions
        show_frontend_instructions()
        
        print("\n🎉 应用启动完成!")
        print("   后端服务正在运行...")
        print("   请按照上面的说明启动前端应用")
        
        # Keep the main thread alive
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        print(f"❌ 应用启动过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
