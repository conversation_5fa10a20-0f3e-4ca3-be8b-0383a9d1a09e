import os
from backend.services.multimodal_asr_service import MultimodalASRService

# --- Configuration ---
# These should be loaded from a secure location in a real application
OPENAI_API_KEY = "EMPTY"
OPENAI_API_BASE = "http://172.16.1.151:20257/v1"
MODEL = "Qwen2-Audio-7B-Instruct"
AUDIO_FILE_PATH = os.path.abspath("welcome.mp3")

def main():
    """
    Main function to test the MultimodalASRService.
    """
    # 1. Initialize the service
    print("Initializing MultimodalASRService...")
    service = MultimodalASRService(
        model=MODEL,
        api_key=OPENAI_API_KEY,
        api_base=OPENAI_API_BASE,
    )
    print("Service initialized.")

    # 2. Check if the audio file exists
    if not os.path.exists(AUDIO_FILE_PATH):
        print(f"Error: Audio file not found at '{AUDIO_FILE_PATH}'")
        print("Please make sure the audio file is downloaded and in the correct path.")
        return

    # 3. Transcribe the audio file
    print(f"\\nTranscribing audio file: {AUDIO_FILE_PATH}...")
    result = service.transcribe_from_file(AUDIO_FILE_PATH)
    print("\\n--- Transcription Result ---")
    if "error" in result:
        print(f"An error occurred: {result['error']}")
    else:
        print(f"Transcription: {result.get('text')}")
    print("--------------------------")


if __name__ == "__main__":
    main()
