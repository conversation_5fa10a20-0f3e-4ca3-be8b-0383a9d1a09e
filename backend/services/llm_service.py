import asyncio
import json
import re
import requests
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
import aiohttp
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SpeakContent:
    emotion: str
    speed: float
    text: str

class LLMService:
    def __init__(self, api_key: str, endpoint: str):
        self.api_key = api_key
        self.endpoint = endpoint
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def extract_speak_content(self, response_text: str) -> Optional[SpeakContent]:
        """Extract SPEAK content from LLM response"""
        try:
            # Use regex to find SPEAK tags
            speak_pattern = r'<SPEAK><emotion>(.*?)</emotion><speed>(.*?)</speed><text>(.*?)</text></SPEAK>'
            match = re.search(speak_pattern, response_text, re.DOTALL)
            
            if match:
                emotion = match.group(1).strip()
                speed = float(match.group(2).strip())
                text = match.group(3).strip()
                
                return SpeakContent(emotion=emotion, speed=speed, text=text)
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting SPEAK content: {e}")
            return None
    
    def build_conversation_messages(self, conversation_history: List[Dict[str, str]], 
                                  system_prompt: str) -> List[Dict[str, str]]:
        """Build conversation messages for the API"""
        messages = [{"role": "system", "content": system_prompt}]
        
        for msg in conversation_history:
            messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })
        
        return messages
    
    async def stream_chat_completion(self, 
                                   messages: List[Dict[str, str]], 
                                   model: str = "doubao-pro-4k",
                                   temperature: float = 0.7,
                                   max_tokens: int = 2000,
                                   tools: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """Stream chat completion from Volcano Engine API"""
        try:
            payload = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": True
            }
            
            if tools:
                payload["tools"] = tools
                payload["tool_choice"] = "auto"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.endpoint,
                    headers=self.headers,
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"API request failed: {response.status} - {error_text}")
                        return
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        
                        if line.startswith('data: '):
                            data = line[6:]  # Remove 'data: ' prefix
                            
                            if data == '[DONE]':
                                break
                            
                            try:
                                chunk = json.loads(data)
                                if 'choices' in chunk and len(chunk['choices']) > 0:
                                    delta = chunk['choices'][0].get('delta', {})
                                    content = delta.get('content', '')
                                    
                                    if content:
                                        yield content
                                        
                            except json.JSONDecodeError:
                                continue
                                
        except Exception as e:
            logger.error(f"Error in stream_chat_completion: {e}")
            yield f"Error: {str(e)}"
    
    async def get_chat_completion(self, 
                                messages: List[Dict[str, str]], 
                                model: str = "doubao-pro-4k",
                                temperature: float = 0.7,
                                max_tokens: int = 2000,
                                tools: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Get complete chat response (non-streaming)"""
        try:
            payload = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False
            }
            
            if tools:
                payload["tools"] = tools
                payload["tool_choice"] = "auto"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.endpoint,
                    headers=self.headers,
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"API request failed: {response.status} - {error_text}")
                        return {"error": f"API request failed: {response.status}"}
                    
                    result = await response.json()
                    return result
                    
        except Exception as e:
            logger.error(f"Error in get_chat_completion: {e}")
            return {"error": str(e)}
    
    def get_default_tools(self) -> List[Dict]:
        """Get default tool definitions for function calling"""
        return [
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "Get current weather information for a location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "The city and state/country, e.g. San Francisco, CA"
                            },
                            "unit": {
                                "type": "string",
                                "enum": ["celsius", "fahrenheit"],
                                "description": "Temperature unit"
                            }
                        },
                        "required": ["location"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_web",
                    "description": "Search the web for current information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query"
                            },
                            "num_results": {
                                "type": "integer",
                                "description": "Number of results to return",
                                "default": 5
                            }
                        },
                        "required": ["query"]
                    }
                }
            }
        ]
    
    async def process_tool_calls(self, tool_calls: List[Dict]) -> List[Dict]:
        """Process tool calls and return results"""
        results = []
        
        for tool_call in tool_calls:
            try:
                function_name = tool_call["function"]["name"]
                arguments = json.loads(tool_call["function"]["arguments"])
                
                # Mock tool implementations
                if function_name == "get_weather":
                    result = {
                        "location": arguments.get("location"),
                        "temperature": "22°C",
                        "condition": "Sunny",
                        "humidity": "65%"
                    }
                elif function_name == "search_web":
                    result = {
                        "query": arguments.get("query"),
                        "results": [
                            {"title": "Sample Result 1", "url": "https://example.com/1"},
                            {"title": "Sample Result 2", "url": "https://example.com/2"}
                        ]
                    }
                else:
                    result = {"error": f"Unknown function: {function_name}"}
                
                results.append({
                    "tool_call_id": tool_call["id"],
                    "role": "tool",
                    "name": function_name,
                    "content": json.dumps(result)
                })
                
            except Exception as e:
                logger.error(f"Error processing tool call: {e}")
                results.append({
                    "tool_call_id": tool_call.get("id", "unknown"),
                    "role": "tool",
                    "name": tool_call.get("function", {}).get("name", "unknown"),
                    "content": json.dumps({"error": str(e)})
                })
        
        return results
    
    async def generate_response_stream(self, 
                                     user_input: str,
                                     conversation_history: List[Dict[str, str]],
                                     system_prompt: str,
                                     use_tools: bool = True) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate streaming response with SPEAK content extraction"""
        try:
            # Build messages
            messages = self.build_conversation_messages(conversation_history, system_prompt)
            messages.append({"role": "user", "content": user_input})
            
            # Get tools if enabled
            tools = self.get_default_tools() if use_tools else None
            
            # Collect full response for processing
            full_response = ""
            
            # Stream the response
            async for chunk in self.stream_chat_completion(messages, tools=tools):
                full_response += chunk
                
                # Yield chunk for real-time streaming
                yield {
                    "type": "chunk",
                    "content": chunk,
                    "full_response": full_response
                }
            
            # Extract SPEAK content from full response
            speak_content = self.extract_speak_content(full_response)
            
            if speak_content:
                yield {
                    "type": "speak",
                    "emotion": speak_content.emotion,
                    "speed": speak_content.speed,
                    "text": speak_content.text,
                    "full_response": full_response
                }
            else:
                # Fallback if no SPEAK tags found
                yield {
                    "type": "speak",
                    "emotion": "neutral",
                    "speed": 1.0,
                    "text": full_response,
                    "full_response": full_response
                }
            
            # Final completion signal
            yield {
                "type": "complete",
                "full_response": full_response
            }
            
        except Exception as e:
            logger.error(f"Error in generate_response_stream: {e}")
            yield {
                "type": "error",
                "error": str(e)
            }
    
    def _generate_mock_response(self, user_input: str) -> Dict[str, Any]:
        """Generate mock response for testing when API is unavailable"""
        # Simple mock responses for common test cases
        mock_responses = {
            "1+1等于几": "1+1等于2。这是一个简单的数学问题。",
            "你好": "你好！很高兴见到你！",
            "天气怎么样": "今天天气不错，阳光明媚。"
        }
        
        # Get mock response or generate a generic one
        if user_input in mock_responses:
            response_text = mock_responses[user_input]
        else:
            response_text = f"我理解你说的是：{user_input}。这是一个测试响应。"
        
        # Format as SPEAK content
        formatted_response = f"<turn>\n<THINK>\n## 1. 意图分析: 用户询问：{user_input}\n## 2. 行动规划: 提供直接回答\n## 3. 工具选择与参数构建: 无需工具\n## 4. 回应构建: <SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>{response_text}</text></SPEAK>\n## 5. 最终输出序列: <SPEAK>\n</THINK>\n<SPEAK><emotion>neutral</emotion><speed>1.0</speed><text>{response_text}</text></SPEAK>\n<JUDGE>\n本轮表现：8/10分\n优点：回答准确直接\n缺点：无\n重大失误：无\n</JUDGE>\n</turn>"
        
        return {
            "success": True,
            "full_response": formatted_response,
            "speak_content": {
                "emotion": "neutral",
                "speed": 1.0,
                "text": response_text
            },
            "mock_mode": True
        }

    async def generate_response(self, 
                              user_input: str,
                              conversation_history: List[Dict[str, str]],
                              system_prompt: str,
                              use_tools: bool = True) -> Dict[str, Any]:
        """Generate complete response (non-streaming)"""
        try:
            # Build messages
            messages = self.build_conversation_messages(conversation_history, system_prompt)
            messages.append({"role": "user", "content": user_input})
            
            # Get tools if enabled
            tools = self.get_default_tools() if use_tools else None
            
            # Try to get real API response first
            response = await self.get_chat_completion(messages, tools=tools)
            
            if "error" in response:
                # If API fails, use mock response
                logger.warning(f"API failed, using mock response: {response['error']}")
                return self._generate_mock_response(user_input)
            
            # Extract content from real API response
            if "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]
                
                # Extract SPEAK content
                speak_content = self.extract_speak_content(content)
                
                return {
                    "success": True,
                    "full_response": content,
                    "speak_content": {
                        "emotion": speak_content.emotion if speak_content else "neutral",
                        "speed": speak_content.speed if speak_content else 1.0,
                        "text": speak_content.text if speak_content else content
                    }
                }
            else:
                # No content in response, use mock
                logger.warning("No content in API response, using mock response")
                return self._generate_mock_response(user_input)
                
        except Exception as e:
            logger.error(f"Error in generate_response: {e}")
            # Use mock response as fallback
            return self._generate_mock_response(user_input)