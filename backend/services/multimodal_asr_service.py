import base64
import numpy as np
from openai import OpenAI
from typing import Dict, Any
import logging
import os
from .asr_service import ASRService

logger = logging.getLogger(__name__)

def encode_base64_content_from_file(file_path: str) -> str:
    """Encode a local file to a base64 string."""
    with open(file_path, "rb") as file:
        return base64.b64encode(file.read()).decode("utf-8")

class MultimodalASRService(ASRService):
    def __init__(self, model: str, api_key: str, api_base: str):
        self.model = model
        self.client = OpenAI(api_key=api_key, base_url=api_base)
        # The parent __init__ is not called as we are not using a local ONNX model.
        # If you need to support both, you would call super().__init__ and handle the logic.

    def transcribe(self, audio_data: np.ndarray, audio_format: str = "wav") -> Dict[str, Any]:
        """
        Transcribe audio to text using a multimodal model.

        Args:
            audio_data: Audio data as a base64 encoded string.
            audio_format: The format of the audio data.

        Returns:
            Dictionary with transcription results.
        """
        try:
            chat_completion = self.client.chat.completions.create(
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "What's in this audio?"},
                            {
                                "type": "audio_url",
                                "audio_url": {
                                    "url": f"data:audio/{audio_format};base64,{audio_data}"
                                },
                            },
                        ],
                    }
                ],
                model=self.model,
                max_completion_tokens=64,
            )
            result = chat_completion.choices[0].message.content
            return {
                "text": result,
                "confidence": 1.0,  # Assuming high confidence for API results
                "tokens": [],  # Not available from API
                "duration": 0.0  # Duration not calculated here
            }
        except Exception as e:
            logger.error(f"Multimodal ASR transcription error: {e}")
            return {
                "text": "",
                "confidence": 0.0,
                "tokens": [],
                "duration": 0.0,
                "error": str(e)
            }

    def transcribe_from_file(self, file_path: str) -> Dict[str, Any]:
        """
        Transcribe an audio file using a multimodal model.

        Args:
            file_path: The path to the audio file.

        Returns:
            Dictionary with transcription results.
        """
        try:
            audio_base64 = encode_base64_content_from_file(file_path)
            file_extension = os.path.splitext(file_path)[1].lstrip('.')
            return self.transcribe(audio_base64, audio_format=file_extension)
        except Exception as e:
            logger.error(f"Error transcribing from file: {e}")
            return {"text": "", "error": str(e)}
