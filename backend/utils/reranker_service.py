import os
import dashscope
from http import HTTPStatus
from typing import List, Dict, Any, Optional
import json
import logging
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RerankerService:
    """
    Reranker服务类，用于衡量用户问题和被调用工具相关性
    使用DashScope的文本重排序模型
    """
    
    def __init__(self):
        """
        初始化Reranker服务
        从环境变量中获取DASHSCOPE_API_KEY
        """
        self.api_key = os.getenv("DASHSCOPE_API_KEY")
        if not self.api_key:
            raise ValueError("DASHSCOPE_API_KEY环境变量未设置")
        
        # 设置DashScope API密钥
        dashscope.api_key = self.api_key
        
        # 默认模型
        self.default_model = "gte-rerank-v2"
        
        logger.info("Reranker服务初始化成功")
    
    def calculate_relevance(self, query: str, documents: List[str], top_n: int = 10) -> Optional[List[Dict[str, Any]]]:
        """
        计算查询与文档列表的相关性得分
        
        Args:
            query (str): 用户查询
            documents (List[str]): 文档列表
            top_n (int): 返回前N个最相关的文档，默认为10
            
        Returns:
            Optional[List[Dict[str, Any]]]: 相关性得分列表，如果失败则返回None
        """
        # 处理边界情况
        if not query or not documents:
            logger.warning("查询或文档列表为空")
            return []
        
        # 重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 调用DashScope文本重排序API
                resp = dashscope.TextReRank.call(
                    model=self.default_model,
                    query=query,
                    documents=documents,
                    top_n=top_n,
                    return_documents=True
                )
                
                if resp.status_code == HTTPStatus.OK:
                    # 返回重排序结果
                    return resp.output.results
                else:
                    logger.error(f"文本重排序API调用失败: {resp}")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # 指数退避
                    continue
                    
            except Exception as e:
                logger.error(f"计算相关性时出错: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                continue
        
        return None
    
    def rank_tools_by_relevance(self, user_question: str, tool_descriptions: List[Dict[str, str]]) -> Optional[List[Dict[str, Any]]]:
        """
        根据用户问题对工具描述进行相关性排序
        
        Args:
            user_question (str): 用户问题
            tool_descriptions (List[Dict[str, str]]): 工具描述列表，每个元素包含name和description
            
        Returns:
            Optional[List[Dict[str, Any]]]: 排序后的工具列表，包含相关性得分
        """
        try:
            # 构造文档列表，每个文档是工具的名称和描述的组合
            documents = []
            tool_mapping = {}  # 用于映射文档索引到工具信息
            
            for i, tool in enumerate(tool_descriptions):
                doc_text = f"工具名称: {tool['name']}\n工具描述: {tool['description']}"
                documents.append(doc_text)
                tool_mapping[i] = tool
            
            # 计算相关性
            results = self.calculate_relevance(user_question, documents)
            
            if results is None:
                return None
            
            # 重新组织结果，添加工具信息
            ranked_tools = []
            for result in results:
                index = result['index']
                if index in tool_mapping:
                    tool_info = tool_mapping[index].copy()
                    tool_info['relevance_score'] = result['relevance_score']
                    ranked_tools.append(tool_info)
            
            return ranked_tools
            
        except Exception as e:
            logger.error(f"对工具进行相关性排序时出错: {e}")
            return None
    
    def get_top_relevant_tool(self, user_question: str, tool_descriptions: List[Dict[str, str]]) -> Optional[Dict[str, Any]]:
        """
        获取与用户问题最相关的工具
        
        Args:
            user_question (str): 用户问题
            tool_descriptions (List[Dict[str, str]]): 工具描述列表
            
        Returns:
            Optional[Dict[str, Any]]: 最相关的工具信息，包含相关性得分
        """
        try:
            ranked_tools = self.rank_tools_by_relevance(user_question, tool_descriptions)
            
            if ranked_tools is None or len(ranked_tools) == 0:
                return None
            
            # 返回最相关的工具（第一个）
            return ranked_tools[0]
            
        except Exception as e:
            logger.error(f"获取最相关工具时出错: {e}")
            return None

# 创建全局实例，方便其他模块直接导入使用
try:
    reranker_service = RerankerService()
except ValueError:
    # 如果没有API密钥，创建一个模拟的服务
    from unittest.mock import Mock
    reranker_service = Mock()
    reranker_service.rank_tools_by_relevance.return_value = None
    reranker_service.get_top_relevant_tool.return_value = None

if __name__ == "__main__":
    # 示例用法
    service = RerankerService()
    
    # 测试相关性计算
    query = "什么是文本排序模型"
    documents = [
        "文本排序模型广泛用于搜索引擎和推荐系统中，它们根据文本相关性对候选文本进行排序",
        "量子计算是计算科学的一个前沿领域",
        "预训练语言模型的发展给文本排序模型带来了新的进展"
    ]
    
    results = service.calculate_relevance(query, documents)
    if results:
        print("相关性计算结果:")
        for result in results:
            print(f"文档: {result['document']['text']}")
            print(f"相关性得分: {result['relevance_score']}")
            print("---")
    
    # 测试工具相关性排序
    user_question = "我想了解最新的科技新闻"
    tool_descriptions = [
        {
            "name": "fetch_news",
            "description": "获取新闻。可以根据多种条件进行筛选和排序，比如获取用户已读的、未读的、最近读过的，或者根据特定主题进行搜索。"
        },
        {
            "name": "search_and_summarize",
            "description": "搜索并总结网络信息。当用户需要了解实时信息或超出个人知识范围的内容时使用。"
        },
        {
            "name": "recall_current_activity",
            "description": "回忆当前活动。用于获取角色当前的状态和正在进行的活动。"
        }
    ]
    
    ranked_tools = service.rank_tools_by_relevance(user_question, tool_descriptions)
    if ranked_tools:
        print("\n工具相关性排序结果:")
        for i, tool in enumerate(ranked_tools):
            print(f"{i+1}. 工具名称: {tool['name']}")
            print(f"   工具描述: {tool['description']}")
            print(f"   相关性得分: {tool['relevance_score']}")
            print("---")
