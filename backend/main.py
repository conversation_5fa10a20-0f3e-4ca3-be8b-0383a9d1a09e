import asyncio
import os
import logging
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
import uvicorn
from supabase import create_client, Client
import numpy as np
import lib<PERSON>a
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import base64
from dotenv import load_dotenv

from services.vad_service import VADService
from services.multimodal_asr_service import MultimodalASRService
from services.llm_service import LLMService
from services.tts_service import TTSService
from services.mcp_service import MCPService

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="Real-time Voice Chat API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# Add request logging middleware
@app.middleware("http")
async def log_requests(request, call_next):
    start_time = datetime.now()
    logger.info(f"📥 {request.method} {request.url} - Headers: {dict(request.headers)}")
    
    response = await call_next(request)
    
    process_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"📤 {request.method} {request.url} - Status: {response.status_code} - Time: {process_time:.3f}s")
    
    return response

# Initialize Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_KEY")  # 使用服务密钥而不是普通密钥
supabase: Client = create_client(supabase_url, supabase_key)

# Initialize services
vad_service = VADService(os.getenv("SILERO_VAD_MODEL_PATH", "./models/silero_vad.onnx"))
asr_service = MultimodalASRService(
    model="Qwen2-Audio-7B-Instruct",
    api_key="EMPTY",
    api_base="http://172.16.1.151:20257/v1"
    # os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
)
llm_service = LLMService(os.getenv("VOLCANO_API_KEY"), os.getenv("VOLCANO_ENDPOINT"))
tts_service = TTSService(os.getenv("MINIMAX_API_KEY"), os.getenv("MINIMAX_GROUP_ID"))
mcp_service = MCPService()

# Simple in-memory user storage for testing
SIMPLE_USERS = {
    "test_user": {
        "id": 1,
        "username": "test_user",
        "password": "test_password",  # 简化版本，直接存储明文密码用于测试
        "email": "<EMAIL>",
        "nickname": "测试用户",
        "is_active": True
    },
    "admin": {
        "id": 2,
        "username": "admin", 
        "password": "admin123",
        "email": "<EMAIL>",
        "nickname": "管理员",
        "is_active": True
    }
}

logger.info("Simple auth system initialized with test users")

# Global connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, Dict] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_sessions[user_id] = {
            "session_id": None,
            "npc_id": None,
            "conversation_history": [],
            "is_speaking": False,
            "audio_buffer": []
        }
        logger.info(f"User {user_id} connected")
    
    def disconnect(self, user_id: str):
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
        logger.info(f"User {user_id} disconnected")
    
    async def send_message(self, user_id: str, message: dict):
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {user_id}: {e}")

manager = ConnectionManager()

# Helper functions
async def get_user_by_id(user_id: int) -> Optional[Dict]:
    """Get user from database"""
    try:
        result = supabase.table("users").select("*").eq("id", user_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        logger.error(f"Error getting user: {e}")
        return None

async def get_npc_by_id(npc_id: int) -> Optional[Dict]:
    """Get NPC from database"""
    try:
        result = supabase.table("npcs").select("*").eq("id", npc_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        logger.error(f"Error getting NPC: {e}")
        return None

async def create_conversation_session(user_id: int, npc_id: int) -> Optional[str]:
    """Create new conversation session"""
    try:
        result = supabase.table("conversation_sessions").insert({
            "user_id": user_id,
            "npc_id": npc_id,
            "is_active": True
        }).execute()
        return result.data[0]["id"] if result.data else None
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        return None

async def save_message(session_id: str, role: str, content: str, audio_url: Optional[str] = None, 
                      emotion: Optional[str] = None, speed: Optional[float] = None):
    """Save message to database"""
    try:
        supabase.table("conversation_messages").insert({
            "session_id": session_id,
            "role": role,
            "content": content,
            "audio_url": audio_url,
            "emotion": emotion,
            "speed": speed
        }).execute()
    except Exception as e:
        logger.error(f"Error saving message: {e}")

# API Routes
@app.get("/")
async def root():
    return {"message": "Real-time Voice Chat API", "status": "running"}

@app.get("/debug/users")
async def debug_users():
    """Debug endpoint to check users"""
    global SIMPLE_USERS
    return {"users": list(SIMPLE_USERS.keys()), "count": len(SIMPLE_USERS)}

@app.get("/test")
async def test_endpoint():
    """Test endpoint for verifying connectivity"""
    test_question = "1+1等于几"
    
    # 创建假的 NPC 数据用于测试
    fake_npc = {
        "id": 1,
        "name": "测试助手",
        "description": "用于测试的AI助手",
        "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。对于数学问题，请直接给出答案。",
        "is_active": True
    }
    
    try:
        # 首先尝试从数据库获取 NPC
        npc = None
        db_available = False
        
        try:
            npc = await get_npc_by_id(1)
            if npc:
                db_available = True
                logger.info("从数据库获取到 NPC 数据")
        except Exception as db_e:
            logger.warning(f"数据库不可用，使用假数据: {db_e}")
        
        # 如果数据库不可用或没有数据，使用假数据
        if not npc:
            npc = fake_npc
            logger.info("使用假 NPC 数据进行测试")
        
        # 测试 LLM 响应
        try:
            response = await llm_service.generate_response(
                test_question,
                [],
                npc["system_prompt"]
            )
            
            if not response.get("success"):
                return {
                    "error": "LLM 处理失败", 
                    "details": response,
                    "database_status": "available" if db_available else "unavailable"
                }
                
            return {
                "question": test_question,
                "response": response["speak_content"]["text"],
                "npc_id": npc["id"],
                "npc_name": npc["name"],
                "database_status": "available" if db_available else "unavailable (using fake data)",
                "status": "success"
            }
        except Exception as llm_e:
            logger.error(f"LLM 服务错误: {llm_e}")
            return {
                "error": f"LLM 服务错误: {str(llm_e)}",
                "database_status": "available" if db_available else "unavailable"
            }
            
    except Exception as e:
        logger.error(f"测试端点错误: {e}")
        return {"error": f"测试端点错误: {str(e)}"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    services_status = {}
    
    # Check VAD service
    try:
        vad_status = "loaded" if hasattr(vad_service, 'session') and vad_service.session else "not loaded"
        services_status["vad"] = vad_status
    except Exception as e:
        services_status["vad"] = f"error: {str(e)}"
    
    # Check ASR service
    try:
        asr_status = "ready" if hasattr(asr_service, 'client') and asr_service.client else "configured"
        services_status["asr"] = asr_status
    except Exception as e:
        services_status["asr"] = f"error: {str(e)}"
    
    # Check LLM service
    try:
        llm_status = "configured" if llm_service else "not configured"
        services_status["llm"] = llm_status
    except Exception as e:
        services_status["llm"] = f"error: {str(e)}"
    
    # Check TTS service
    try:
        tts_status = "configured" if tts_service else "not configured"
        services_status["tts"] = tts_status
    except Exception as e:
        services_status["tts"] = f"error: {str(e)}"
    
    # Check MCP service
    try:
        mcp_status = "ready" if mcp_service else "not configured"
        services_status["mcp"] = mcp_status
    except Exception as e:
        services_status["mcp"] = f"error: {str(e)}"
    
    # Check database connection
    try:
        result = supabase.table("npcs").select("count", count="exact").limit(1).execute()
        services_status["database"] = "connected"
    except Exception as e:
        services_status["database"] = f"error: {str(e)}"
    
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": services_status,
        "version": "1.0.0"
    }

@app.get("/npcs")
async def get_npcs():
    """Get available NPCs"""
    # 假数据作为备用
    fake_npcs = [
        {
            "id": 1,
            "name": "默认助手",
            "description": "通用AI助手，用于测试",
            "avatar_url": None
        },
        {
            "id": 2,
            "name": "朋友",
            "description": "亲密朋友角色，轻松聊天",
            "avatar_url": None
        }
    ]
    
    try:
        result = supabase.table("npcs").select("id, name, description, avatar_url").eq("is_active", True).execute()
        if result.data:
            return {"npcs": result.data, "source": "database"}
        else:
            logger.warning("数据库中没有 NPC 数据，返回假数据")
            return {"npcs": fake_npcs, "source": "fake_data"}
    except Exception as e:
        logger.warning(f"数据库获取 NPCs 失败，返回假数据: {e}")
        return {"npcs": fake_npcs, "source": "fake_data"}

@app.post("/auth/register")
@app.get("/auth/register")
@app.options("/auth/register")
async def register_user(username: str, password: str, email: str = None, nickname: str = None):
    """User registration endpoint"""
    global SIMPLE_USERS
    
    try:
        print("hahahh")
        print("SIMPLE_USERS:",SIMPLE_USERS)
        if username in SIMPLE_USERS:
            raise HTTPException(status_code=400, detail="Username already exists")
        
        user_id = len(SIMPLE_USERS) + 1
        SIMPLE_USERS[username] = {
            "id": user_id,
            "username": username,
            "password": password,
            "email": email,
            "nickname": nickname or username,
            "is_active": True
        }
        print("SIMPLE_USERS:",SIMPLE_USERS)
        user = SIMPLE_USERS[username]
        from datetime import datetime
        now = datetime.now().isoformat()
        return {
            "user": {
                "id": user["id"],
                "uuid": f"user_{user['id']}_{username}",
                "username": user["username"],
                "email": user.get("email"),
                "nickname": user["nickname"],
                "avatar_url": user.get("avatar_url"),
                "created_at": now,
                "updated_at": now
            },
            "token": f"token_{user['id']}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(status_code=500, detail="Registration failed")

@app.post("/auth/login")
@app.get("/auth/login")
@app.options("/auth/login")
async def login_user(username: str, password: str):
    """User login endpoint"""
    global SIMPLE_USERS
    
    logger.info(f"🔐 Login attempt: username={username}, password={'*' * len(password)}")
    logger.info(f"🔐 Available users: {list(SIMPLE_USERS.keys())}")
    try:
        
        logger.info(f"Available users: {list(SIMPLE_USERS.keys())}")
        
        if username not in SIMPLE_USERS:
            logger.warning(f"User not found: {username}")
            raise HTTPException(status_code=401, detail="Invalid username or password")
        
        user = SIMPLE_USERS[username]
        
        if not user.get("is_active", True):
            logger.warning(f"User account disabled: {username}")
            raise HTTPException(status_code=401, detail="Account is disabled")
        
        if user["password"] != password:
            logger.warning(f"Invalid password for user: {username}")
            raise HTTPException(status_code=401, detail="Invalid username or password")
        
        logger.info(f"✅ Login successful for user: {username}")
        from datetime import datetime
        now = datetime.now().isoformat()
        return {
            "user": {
                "id": user["id"],
                "uuid": f"user_{user['id']}_{username}",
                "username": user["username"],
                "email": user.get("email"),
                "nickname": user["nickname"],
                "avatar_url": user.get("avatar_url"),
                "created_at": now,
                "updated_at": now
            },
            "token": f"token_{user['id']}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="Login failed")

@app.post("/process-audio")
async def process_audio_file(file: UploadFile = File(...), user_id: int = 1, npc_id: int = 1):
    """Process uploaded audio file"""
    try:
        # Read audio file
        audio_data = await file.read()
        
        # Convert to numpy array (assuming WAV format)
        audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
        
        # VAD processing
        speech_segments = vad_service.detect_speech_segments(audio_array)
        
        if not speech_segments:
            return {"error": "No speech detected"}
        
        # Extract speech audio
        start_sample = int(speech_segments[0][0] * 16000)
        end_sample = int(speech_segments[0][1] * 16000)
        speech_audio = audio_array[start_sample:end_sample]
        
        # ASR processing
        transcription = asr_service.transcribe(speech_audio)
        
        if not transcription["text"]:
            return {"error": "No text transcribed"}
        
        # Get NPC and conversation history (with fallback)
        npc = await get_npc_by_id(npc_id)
        if not npc:
            # Use fallback NPC data
            fake_npc = {
                "id": npc_id,
                "name": "测试助手",
                "description": "用于测试的AI助手",
                "system_prompt": "你是一个友善的AI助手，请用自然的语调回复用户。",
                "is_active": True
            }
            npc = fake_npc
            logger.info(f"使用假 NPC 数据进行音频处理，NPC ID: {npc_id}")
        
        # LLM processing
        response = await llm_service.generate_response(
            transcription["text"],
            [],  # Empty history for this example
            npc["system_prompt"]
        )
        
        if not response.get("success"):
            return {"error": "LLM processing failed"}
        
        # TTS processing
        speak_content = response["speak_content"]
        tts_result = await tts_service.synthesize_speech(
            speak_content["text"],
            speak_content["emotion"],
            speak_content["speed"]
        )
        
        if not tts_result["success"]:
            return {"error": "TTS processing failed"}
        
        # Return results
        return {
            "transcription": transcription["text"],
            "response_text": speak_content["text"],
            "emotion": speak_content["emotion"],
            "speed": speak_content["speed"],
            "audio_size": tts_result["size"]
        }
        
    except Exception as e:
        logger.error(f"Audio processing error: {e}")
        raise HTTPException(status_code=500, detail="Audio processing failed")

# MCP API Routes
@app.get("/mcp/servers")
async def get_mcp_servers():
    """Get all registered MCP servers"""
    try:
        servers = mcp_service.list_servers()
        return {"servers": servers}
    except Exception as e:
        logger.error(f"获取MCP服务器列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取MCP服务器列表失败")

@app.get("/mcp/tools")
async def get_mcp_tools():
    """Get all tools from all MCP servers for reranker"""
    try:
        tools = mcp_service.get_all_tools_for_reranker()
        return {"tools": tools}
    except Exception as e:
        logger.error(f"获取MCP工具列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取MCP工具列表失败")

@app.get("/mcp/servers/{server_name}/tools")
async def get_server_tools(server_name: str):
    """Get tools from specific MCP server"""
    try:
        tools = mcp_service.get_server_tools(server_name)
        return {"server_name": server_name, "tools": tools}
    except Exception as e:
        logger.error(f"获取服务器 '{server_name}' 工具列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取服务器 '{server_name}' 工具列表失败")

@app.post("/mcp/tools/execute")
async def execute_mcp_tool(tool_name: str, server_name: str = None, parameters: Dict[str, Any] = None):
    """Execute MCP tool"""
    try:
        if parameters is None:
            parameters = {}
        result = mcp_service.execute_tool(tool_name, server_name, **parameters)
        return result
    except Exception as e:
        logger.error(f"执行MCP工具 '{tool_name}' 失败: {e}")
        raise HTTPException(status_code=500, detail=f"执行MCP工具 '{tool_name}' 失败")

# WebSocket endpoint for real-time communication
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await manager.connect(websocket, user_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "start_session":
                # Start new conversation session
                npc_id = message.get("npc_id", 1)
                session_id = await create_conversation_session(int(user_id), npc_id)
                
                if session_id:
                    manager.user_sessions[user_id]["session_id"] = session_id
                    manager.user_sessions[user_id]["npc_id"] = npc_id
                    
                    await manager.send_message(user_id, {
                        "type": "session_started",
                        "session_id": session_id,
                        "npc_id": npc_id
                    })
                else:
                    await manager.send_message(user_id, {
                        "type": "error",
                        "message": "Failed to start session"
                    })
            
            elif message_type == "audio_chunk":
                # Process audio chunk
                logger.info(f"📥 Received audio chunk from user {user_id}, size: {len(message.get('data', ''))} chars")
                audio_data = base64.b64decode(message["data"])
                logger.info(f"📦 Decoded audio data size: {len(audio_data)} bytes")
                await process_audio_chunk(user_id, audio_data)
            
            elif message_type == "interrupt":
                # Handle user interruption
                manager.user_sessions[user_id]["is_speaking"] = False
                await manager.send_message(user_id, {
                    "type": "interrupted",
                    "message": "Assistant interrupted"
                })
            
            elif message_type == "end_session":
                # End conversation session
                session_id = manager.user_sessions[user_id].get("session_id")
                if session_id:
                    try:
                        supabase.table("conversation_sessions").update({
                            "ended_at": datetime.now().isoformat(),
                            "is_active": False
                        }).eq("id", session_id).execute()
                    except Exception as e:
                        logger.error(f"Error ending session: {e}")
                
                await manager.send_message(user_id, {
                    "type": "session_ended"
                })
    
    except WebSocketDisconnect:
        manager.disconnect(user_id)
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {e}")
        manager.disconnect(user_id)

async def process_audio_chunk(user_id: str, audio_data: bytes):
    """Process incoming audio chunk"""
    try:
        session = manager.user_sessions.get(user_id)
        if not session:
            return
        
        # Convert audio data to numpy array
        audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
        
        # Add to buffer
        session["audio_buffer"].extend(audio_array)
        
        # VAD processing on buffer
        if len(session["audio_buffer"]) >= 16000:  # 1 second of audio
            buffer_array = np.array(session["audio_buffer"])
            
            # Check for speech
            is_speech, confidence = vad_service.detect_speech(buffer_array[-8000:])  # Last 0.5 seconds
            
            if is_speech and confidence > 0.7:
                # Process accumulated audio
                await process_speech_segment(user_id, buffer_array)
                session["audio_buffer"] = []
            elif len(session["audio_buffer"]) > 48000:  # 3 seconds max buffer
                session["audio_buffer"] = session["audio_buffer"][-16000:]  # Keep last 1 second
    
    except Exception as e:
        logger.error(f"Error processing audio chunk: {e}")

async def process_speech_segment(user_id: str, audio_data: np.ndarray):
    """Process complete speech segment"""
    try:
        session = manager.user_sessions.get(user_id)
        if not session or session["is_speaking"]:
            return
        
        session["is_speaking"] = True
        
        # ASR processing
        transcription = asr_service.transcribe(audio_data)
        
        if not transcription["text"] or transcription["confidence"] < 0.5:
            session["is_speaking"] = False
            return
        
        # Send transcription to client
        await manager.send_message(user_id, {
            "type": "transcription",
            "text": transcription["text"],
            "confidence": transcription["confidence"]
        })
        
        # Get NPC prompt
        npc_id = session.get("npc_id", 1)
        npc = await get_npc_by_id(npc_id)
        
        if not npc:
            session["is_speaking"] = False
            return
        
        # LLM processing with streaming
        async for chunk in llm_service.generate_response_stream(
            transcription["text"],
            session["conversation_history"],
            npc["system_prompt"]
        ):
            if chunk["type"] == "speak":
                # TTS processing
                async for audio_chunk in tts_service.synthesize_speech_stream(
                    chunk["text"],
                    chunk["emotion"],
                    chunk["speed"]
                ):
                    if audio_chunk and session["is_speaking"]:
                        # Send audio chunk to client
                        audio_b64 = base64.b64encode(audio_chunk).decode()
                        await manager.send_message(user_id, {
                            "type": "audio_chunk",
                            "data": audio_b64
                        })
                
                # Save messages to database
                session_id = session.get("session_id")
                if session_id:
                    await save_message(session_id, "user", transcription["text"])
                    await save_message(session_id, "assistant", chunk["text"], 
                                     emotion=chunk["emotion"], speed=chunk["speed"])
                
                # Update conversation history
                session["conversation_history"].extend([
                    {"role": "user", "content": transcription["text"]},
                    {"role": "assistant", "content": chunk["text"]}
                ])
                
                break
        
        session["is_speaking"] = False
        
        await manager.send_message(user_id, {
            "type": "response_complete"
        })
    
    except Exception as e:
        logger.error(f"Error processing speech segment: {e}")
        session = manager.user_sessions.get(user_id)
        if session:
            session["is_speaking"] = False

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
