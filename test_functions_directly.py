#!/usr/bin/env python3
"""
直接测试后端函数，不需要启动服务器
"""
import asyncio
import sys
import os

# 添加 backend 目录到路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

async def test_functions():
    """直接测试后端函数"""
    print("🧪 直接测试后端函数...")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from main import test_endpoint, get_npcs, health_check
        
        # 1. 测试健康检查
        print("🏥 测试健康检查...")
        health_result = await health_check()
        print("✅ 健康检查结果:")
        print(f"   状态: {health_result['status']}")
        print(f"   服务: {health_result['services']}")
        
        # 2. 测试 NPCs 端点
        print("\n👥 测试 NPCs 端点...")
        npcs_result = await get_npcs()
        print("✅ NPCs 端点结果:")
        print(f"   数据源: {npcs_result['source']}")
        print(f"   NPC 数量: {len(npcs_result['npcs'])}")
        for npc in npcs_result['npcs']:
            print(f"   - ID: {npc['id']}, 名称: {npc['name']}")
        
        # 3. 测试问答端点
        print("\n🧮 测试 '1+1等于几' 问答...")
        test_result = await test_endpoint()
        print("✅ 测试端点结果:")
        print(f"   状态: {test_result.get('status', 'unknown')}")
        print(f"   问题: {test_result.get('question', 'N/A')}")
        print(f"   回答: {test_result.get('response', 'N/A')}")
        print(f"   数据库状态: {test_result.get('database_status', 'N/A')}")
        print(f"   NPC: {test_result.get('npc_name', 'N/A')} (ID: {test_result.get('npc_id', 'N/A')})")
        
        # 检查是否成功
        if test_result.get('status') == 'success':
            print("\n🎉 所有测试通过！")
            return True
        else:
            print(f"\n❌ 测试失败: {test_result}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_llm_service():
    """单独测试 LLM 服务"""
    print("\n🤖 测试 LLM 服务...")
    
    try:
        from services.llm_service import LLMService
        import os
        
        # 初始化 LLM 服务
        llm_service = LLMService(
            os.getenv("VOLCANO_API_KEY", "test_key"), 
            os.getenv("VOLCANO_ENDPOINT", "test_endpoint")
        )
        
        # 测试生成响应
        response = await llm_service.generate_response(
            "1+1等于几",
            [],
            "你是一个友善的AI助手，请用自然的语调回复用户。"
        )
        
        print("✅ LLM 服务测试结果:")
        print(f"   成功: {response.get('success', False)}")
        print(f"   回答: {response.get('speak_content', {}).get('text', 'N/A')}")
        print(f"   情感: {response.get('speak_content', {}).get('emotion', 'N/A')}")
        print(f"   语速: {response.get('speak_content', {}).get('speed', 'N/A')}")
        
        if response.get('mock_mode'):
            print("   📝 使用模拟模式 (API 不可用)")
        
        return response.get('success', False)
        
    except Exception as e:
        print(f"❌ LLM 服务测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始直接函数测试...")
    
    # 加载环境变量
    from dotenv import load_dotenv
    load_dotenv('backend/.env')
    
    # 运行测试
    success1 = asyncio.run(test_functions())
    success2 = asyncio.run(test_llm_service())
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 所有测试通过！后端功能正常。")
        return 0
    else:
        print("❌ 部分测试失败。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
